"""
性能评估器
提供端到端的模型性能评估和分析
"""

import numpy as np
import time
from typing import Dict, List, Tuple, Optional
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json

from .metrics import TrajectoryMetrics, ClassificationMetrics, RealTimeMetrics
from src.data.radar_data import TargetTrajectory
from config.logging_config import default_logger as logger


class PerformanceEvaluator:
    """性能评估器"""
    
    def __init__(self, save_path: str = "evaluation_results"):
        """
        初始化评估器
        
        Args:
            save_path: 评估结果保存路径
        """
        self.save_path = Path(save_path)
        self.save_path.mkdir(parents=True, exist_ok=True)
        
        self.trajectory_metrics = TrajectoryMetrics()
        self.classification_metrics = ClassificationMetrics()
        self.realtime_metrics = RealTimeMetrics()
        
        self.evaluation_results = {}
    
    def evaluate_trajectory_prediction(self, 
                                     y_true: np.n<PERSON><PERSON>, 
                                     y_pred: np.ndarray,
                                     model_name: str = "LSTM") -> Dict:
        """
        评估航迹预测性能
        
        Args:
            y_true: 真实轨迹
            y_pred: 预测轨迹
            model_name: 模型名称
            
        Returns:
            评估结果
        """
        logger.info(f"评估 {model_name} 航迹预测性能...")
        
        # 分离位置和速度（如果包含）
        if y_true.shape[-1] == 6:  # 位置+速度
            pos_true, vel_true = y_true[:, :, :3], y_true[:, :, 3:]
            pos_pred, vel_pred = y_pred[:, :, :3], y_pred[:, :, 3:]
        else:  # 仅位置
            pos_true, pos_pred = y_true, y_pred
            vel_true = vel_pred = None
        
        # 计算位置误差
        position_metrics = self.trajectory_metrics.position_error(pos_true, pos_pred)
        
        # 计算速度误差（如果有）
        velocity_metrics = {}
        if vel_true is not None and vel_pred is not None:
            velocity_metrics = self.trajectory_metrics.velocity_error(vel_true, vel_pred)
        
        # 计算ADE和FDE
        ade, fde = self.trajectory_metrics.calculate_ade_fde(pos_true, pos_pred)
        
        # 时间一致性
        temporal_consistency = self.trajectory_metrics.temporal_consistency(pos_pred)
        
        # 预测时间步分析
        horizon_analysis = self.trajectory_metrics.prediction_horizon_analysis(pos_true, pos_pred)
        
        results = {
            "model_name": model_name,
            "position_metrics": position_metrics,
            "velocity_metrics": velocity_metrics,
            "ade": ade,
            "fde": fde,
            "temporal_consistency": temporal_consistency,
            "horizon_analysis": horizon_analysis,
            "evaluation_timestamp": time.time()
        }
        
        self.evaluation_results[f"{model_name}_trajectory"] = results
        
        logger.info(f"{model_name} 航迹预测评估完成:")
        logger.info(f"  - 平均位置误差: {position_metrics['mean_position_error']:.2f}m")
        logger.info(f"  - ADE: {ade:.2f}m, FDE: {fde:.2f}m")
        logger.info(f"  - 时间一致性: {temporal_consistency:.3f}")
        
        return results
    
    def evaluate_tactical_classification(self,
                                       y_true: List[str],
                                       y_pred: List[str], 
                                       y_pred_proba: np.ndarray,
                                       class_names: List[str],
                                       model_name: str = "Classifier") -> Dict:
        """
        评估战术分类性能
        
        Args:
            y_true: 真实标签
            y_pred: 预测标签
            y_pred_proba: 预测概率
            class_names: 类别名称
            model_name: 模型名称
            
        Returns:
            评估结果
        """
        logger.info(f"评估 {model_name} 战术分类性能...")
        
        # 详细分类报告
        classification_report = self.classification_metrics.detailed_classification_report(
            y_true, y_pred, class_names
        )
        
        # 置信度校准（需要转换为one-hot）
        y_true_onehot = np.zeros((len(y_true), len(class_names)))
        for i, label in enumerate(y_true):
            if label in class_names:
                y_true_onehot[i, class_names.index(label)] = 1
        
        calibration_metrics = self.classification_metrics.confidence_calibration(
            y_true_onehot, y_pred_proba
        )
        
        # 战术转换分析
        transition_analysis = self.classification_metrics.tactical_transition_analysis(
            y_true, y_pred
        )
        
        results = {
            "model_name": model_name,
            "classification_report": classification_report,
            "calibration_metrics": calibration_metrics,
            "transition_analysis": transition_analysis,
            "evaluation_timestamp": time.time()
        }
        
        self.evaluation_results[f"{model_name}_classification"] = results
        
        # 输出关键指标
        overall_accuracy = classification_report["accuracy"]
        macro_f1 = classification_report["macro avg"]["f1-score"]
        
        logger.info(f"{model_name} 战术分类评估完成:")
        logger.info(f"  - 整体准确率: {overall_accuracy:.3f}")
        logger.info(f"  - 宏平均F1: {macro_f1:.3f}")
        logger.info(f"  - 置信度校准误差: {calibration_metrics['expected_calibration_error']:.3f}")
        
        return results
    
    def evaluate_realtime_performance(self, 
                                    prediction_times: List[float],
                                    processing_times: List[float],
                                    time_window: float = 60.0) -> Dict:
        """
        评估实时性能
        
        Args:
            prediction_times: 预测时间列表（毫秒）
            processing_times: 处理时间列表（毫秒）
            time_window: 时间窗口（秒）
            
        Returns:
            实时性能指标
        """
        logger.info("评估实时性能...")
        
        # 记录时间数据
        for pt in prediction_times:
            self.realtime_metrics.record_prediction_time(pt)
        
        for pt in processing_times:
            self.realtime_metrics.record_processing_time(pt)
        
        # 获取延迟统计
        latency_stats = self.realtime_metrics.get_latency_stats()
        
        # 获取吞吐量统计
        throughput_stats = self.realtime_metrics.get_throughput_stats(time_window)
        
        results = {
            "latency_stats": latency_stats,
            "throughput_stats": throughput_stats,
            "evaluation_timestamp": time.time()
        }
        
        self.evaluation_results["realtime_performance"] = results
        
        logger.info("实时性能评估完成:")
        logger.info(f"  - 平均预测延迟: {latency_stats['mean_prediction_latency_ms']:.2f}ms")
        logger.info(f"  - P95延迟: {latency_stats['p95_prediction_latency_ms']:.2f}ms")
        logger.info(f"  - 吞吐量: {throughput_stats['predictions_per_second']:.2f} pred/s")
        
        return results
    
    def generate_comprehensive_report(self) -> Dict:
        """生成综合评估报告"""
        logger.info("生成综合评估报告...")
        
        report = {
            "evaluation_summary": {
                "timestamp": time.time(),
                "total_evaluations": len(self.evaluation_results),
                "evaluated_components": list(self.evaluation_results.keys())
            },
            "detailed_results": self.evaluation_results
        }
        
        # 保存报告
        report_path = self.save_path / "comprehensive_evaluation_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"综合评估报告已保存: {report_path}")
        
        return report
    
    def plot_performance_analysis(self):
        """绘制性能分析图表"""
        logger.info("生成性能分析图表...")
        
        # 1. 航迹预测误差分析
        if any("trajectory" in key for key in self.evaluation_results.keys()):
            self._plot_trajectory_analysis()
        
        # 2. 分类性能分析
        if any("classification" in key for key in self.evaluation_results.keys()):
            self._plot_classification_analysis()
        
        # 3. 实时性能分析
        if "realtime_performance" in self.evaluation_results:
            self._plot_realtime_analysis()
    
    def _plot_trajectory_analysis(self):
        """绘制航迹预测分析图"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        for key, result in self.evaluation_results.items():
            if "trajectory" not in key:
                continue
            
            model_name = result["model_name"]
            
            # 预测时间步误差
            horizon_analysis = result["horizon_analysis"]
            time_steps = list(horizon_analysis.keys())
            errors = list(horizon_analysis.values())
            
            axes[0, 0].plot(time_steps, errors, marker='o', label=model_name)
            axes[0, 0].set_xlabel('预测时间步')
            axes[0, 0].set_ylabel('平均位置误差 (m)')
            axes[0, 0].set_title('预测误差随时间变化')
            axes[0, 0].legend()
            axes[0, 0].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(self.save_path / "trajectory_performance_analysis.png", dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_classification_analysis(self):
        """绘制分类性能分析图"""
        for key, result in self.evaluation_results.items():
            if "classification" not in key:
                continue
            
            model_name = result["model_name"]
            
            # 混淆矩阵
            cm = np.array(result["classification_report"]["confusion_matrix"])
            
            plt.figure(figsize=(10, 8))
            sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')
            plt.title(f'{model_name} 混淆矩阵')
            plt.ylabel('真实标签')
            plt.xlabel('预测标签')
            plt.savefig(self.save_path / f"{model_name}_confusion_matrix.png", dpi=300, bbox_inches='tight')
            plt.close()
    
    def _plot_realtime_analysis(self):
        """绘制实时性能分析图"""
        if not self.realtime_metrics.prediction_times:
            return
        
        fig, axes = plt.subplots(1, 2, figsize=(15, 5))
        
        # 延迟分布
        axes[0].hist(self.realtime_metrics.prediction_times, bins=30, alpha=0.7, edgecolor='black')
        axes[0].set_xlabel('预测延迟 (ms)')
        axes[0].set_ylabel('频次')
        axes[0].set_title('预测延迟分布')
        axes[0].grid(True, alpha=0.3)
        
        # 延迟时间序列
        axes[1].plot(self.realtime_metrics.prediction_times, alpha=0.7)
        axes[1].set_xlabel('预测次数')
        axes[1].set_ylabel('延迟 (ms)')
        axes[1].set_title('预测延迟时间序列')
        axes[1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(self.save_path / "realtime_performance_analysis.png", dpi=300, bbox_inches='tight')
        plt.close()
