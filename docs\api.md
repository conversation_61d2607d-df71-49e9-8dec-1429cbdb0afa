# API 文档

## REST API 接口

### 基础信息

- **基础URL**: `http://localhost:8000`
- **内容类型**: `application/json`
- **认证**: 暂无（开发版本）

### 端点列表

#### 1. 健康检查

**GET** `/health`

检查API服务的健康状态。

**响应示例**:
```json
{
  "status": "healthy",
  "timestamp": "2025-09-03T04:16:00.000Z",
  "engine_status": "running"
}
```

#### 2. 添加雷达信号

**POST** `/radar/signal`

向系统添加新的雷达信号数据。

**请求体**:
```json
{
  "timestamp": "2025-09-03T04:16:00.000Z",
  "station_id": "radar_001",
  "target_id": "target_001",
  "toa": 0.1,
  "pa": 0.785,
  "position": [1000.0, 2000.0, 10000.0],
  "velocity": [150.0, 50.0, 0.0],
  "heading": 0.5,
  "elevation": 0.1,
  "pitch": 0.05,
  "roll": 0.02,
  "snr": 20.0,
  "confidence": 0.9
}
```

**响应示例**:
```json
{
  "status": "success",
  "message": "雷达信号已添加，目标: target_001",
  "timestamp": "2025-09-03T04:16:00.000Z"
}
```

#### 3. 获取目标预测

**GET** `/prediction/{target_id}`

获取指定目标的预测结果。

**路径参数**:
- `target_id`: 目标ID

**响应示例**:
```json
{
  "target_id": "target_001",
  "timestamp": "2025-09-03T04:16:00.000Z",
  "trajectory_prediction": [
    [1100.0, 2050.0, 10000.0],
    [1200.0, 2100.0, 10000.0],
    ...
  ],
  "tactical_prediction": {
    "predicted_mode": "巡逻",
    "confidence": 0.85,
    "probabilities": {
      "巡逻": 0.85,
      "攻击突击": 0.10,
      "空中格斗": 0.03,
      "预警探测": 0.02
    }
  },
  "data_points": 25,
  "last_position": [1000.0, 2000.0, 10000.0],
  "last_velocity": [150.0, 50.0, 0.0]
}
```

#### 4. 获取所有预测

**GET** `/predictions`

获取所有目标的预测结果。

**响应示例**:
```json
{
  "target_001": { /* 预测结果 */ },
  "target_002": { /* 预测结果 */ }
}
```

#### 5. 获取目标状态

**GET** `/targets/status`

获取所有目标的状态信息。

**响应示例**:
```json
{
  "timestamp": "2025-09-03T04:16:00.000Z",
  "total_targets": 2,
  "targets": {
    "target_001": {
      "data_points": 25,
      "last_update": "2025-09-03T04:15:59.000Z",
      "last_position": [1000.0, 2000.0, 10000.0],
      "last_velocity": [150.0, 50.0, 0.0],
      "has_prediction": true
    }
  }
}
```

## 错误响应

所有错误响应都遵循以下格式：

```json
{
  "detail": "错误描述信息"
}
```

常见错误代码：
- `400`: 请求参数错误
- `404`: 目标未找到
- `500`: 服务器内部错误
- `503`: 服务不可用

## 使用示例

### Python客户端示例

```python
import requests

# 发送雷达信号
signal_data = {
    "timestamp": "2025-09-03T04:16:00.000Z",
    "station_id": "radar_001",
    "target_id": "target_001",
    "toa": 0.1,
    "pa": 0.785,
    "position": [1000.0, 2000.0, 10000.0],
    "velocity": [150.0, 50.0, 0.0]
}

response = requests.post("http://localhost:8000/radar/signal", json=signal_data)
print(response.json())

# 获取预测结果
response = requests.get("http://localhost:8000/prediction/target_001")
prediction = response.json()
print(f"预测模式: {prediction['tactical_prediction']['predicted_mode']}")
```
