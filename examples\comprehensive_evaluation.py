"""
综合性能评估演示
评估增强模型的各项性能指标
"""

import sys
import numpy as np
import time
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.evaluation.performance_evaluator import PerformanceEvaluator
from src.data.enhanced_data_generator import EnhancedDataGenerator
from src.data.preprocessor import DataPreprocessor
import tensorflow as tf

# 设置日志
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def load_models():
    """加载训练好的模型"""
    models = {}
    
    # 加载LSTM模型
    lstm_paths = [
        "data/models/lstm_trajectory_model.h5",
        "data/models/enhanced_lstm_model.h5"
    ]
    
    for path in lstm_paths:
        if Path(path).exists():
            try:
                model = tf.keras.models.load_model(path)
                model_name = "Enhanced_LSTM" if "enhanced" in path else "Basic_LSTM"
                models[model_name] = model
                logger.info(f"加载模型: {model_name}")
            except Exception as e:
                logger.warning(f"加载模型失败 {path}: {e}")
    
    # 加载分类器
    classifier_paths = [
        "data/models/tactical_classifier_model.h5",
        "data/models/enhanced_tactical_classifier.h5"
    ]
    
    for path in classifier_paths:
        if Path(path).exists():
            try:
                model = tf.keras.models.load_model(path)
                model_name = "Enhanced_Classifier" if "enhanced" in path else "Basic_Classifier"
                models[model_name] = model
                logger.info(f"加载模型: {model_name}")
                
                # 加载标签
                label_path = path.replace('.h5', '.labels.npy')
                if Path(label_path).exists():
                    labels = np.load(label_path, allow_pickle=True).item()
                    models[f"{model_name}_labels"] = labels
                
            except Exception as e:
                logger.warning(f"加载分类器失败 {path}: {e}")
    
    return models


def generate_test_data():
    """生成测试数据"""
    logger.info("生成测试数据...")
    
    generator = EnhancedDataGenerator()
    
    # 生成少量测试数据
    test_trajectories = generator.generate_balanced_dataset(
        samples_per_class=10,  # 每类10个样本
        sequence_length=30
    )
    
    logger.info(f"生成了 {len(test_trajectories)} 个测试航迹")
    
    return test_trajectories


def evaluate_lstm_models(models: dict, test_trajectories: list, evaluator: PerformanceEvaluator):
    """评估LSTM模型"""
    logger.info("评估LSTM模型...")
    
    # 准备测试数据
    preprocessor = DataPreprocessor()
    
    # 为基础LSTM准备6维数据
    X_basic, y_basic = [], []
    # 为增强LSTM准备27维数据  
    X_enhanced, y_enhanced = [], []
    
    for trajectory in test_trajectories:
        if len(trajectory.signals) >= 25:
            # 基础特征（6维）
            basic_features = []
            enhanced_features = []
            
            sorted_signals = sorted(trajectory.signals, key=lambda x: x.timestamp)
            recent_signals = sorted_signals[-25:]  # 最近25个点
            
            for signal in recent_signals:
                # 基础特征：位置+速度
                basic_feat = [
                    signal.position[0], signal.position[1], signal.position[2],
                    signal.velocity[0], signal.velocity[1], signal.velocity[2]
                ]
                basic_features.append(basic_feat)
                
                # 增强特征：完整27维
                enhanced_feat = signal.to_feature_vector()
                enhanced_features.append(enhanced_feat)
            
            if len(basic_features) >= 20:
                # 输入序列（前20个点）
                X_basic.append(basic_features[:20])
                X_enhanced.append(enhanced_features[:20])
                
                # 目标序列（后5个点的位置+速度）
                target_basic = []
                target_enhanced = []
                for i in range(20, min(25, len(basic_features))):
                    target_basic.append(basic_features[i])
                    target_enhanced.append(enhanced_features[i][:6])  # 只取位置+速度
                
                # 填充到10个预测步长
                while len(target_basic) < 10:
                    target_basic.append(target_basic[-1])
                    target_enhanced.append(target_enhanced[-1])
                
                y_basic.append(target_basic[:10])
                y_enhanced.append(target_enhanced[:10])
    
    if not X_basic:
        logger.warning("没有足够的测试数据")
        return
    
    X_basic = np.array(X_basic)
    y_basic = np.array(y_basic)
    X_enhanced = np.array(X_enhanced)
    y_enhanced = np.array(y_enhanced)
    
    logger.info(f"基础LSTM测试数据: X{X_basic.shape}, y{y_basic.shape}")
    logger.info(f"增强LSTM测试数据: X{X_enhanced.shape}, y{y_enhanced.shape}")
    
    # 评估基础LSTM
    if "Basic_LSTM" in models:
        try:
            start_time = time.time()
            predictions_basic = models["Basic_LSTM"].predict(X_basic, verbose=0)
            prediction_time = (time.time() - start_time) * 1000
            
            evaluator.evaluate_trajectory_prediction(
                y_basic, predictions_basic, "Basic_LSTM"
            )
            
            logger.info(f"基础LSTM预测时间: {prediction_time:.2f}ms")
            
        except Exception as e:
            logger.error(f"基础LSTM评估失败: {e}")
    
    # 评估增强LSTM
    if "Enhanced_LSTM" in models:
        try:
            start_time = time.time()
            predictions_enhanced = models["Enhanced_LSTM"].predict(X_enhanced, verbose=0)
            prediction_time = (time.time() - start_time) * 1000
            
            evaluator.evaluate_trajectory_prediction(
                y_enhanced, predictions_enhanced, "Enhanced_LSTM"
            )
            
            logger.info(f"增强LSTM预测时间: {prediction_time:.2f}ms")
            
        except Exception as e:
            logger.error(f"增强LSTM评估失败: {e}")


def evaluate_classifiers(models: dict, test_trajectories: list, evaluator: PerformanceEvaluator):
    """评估分类器"""
    logger.info("评估分类器...")
    
    # 准备分类测试数据
    preprocessor = DataPreprocessor()
    
    X_tactical = []
    y_true = []
    
    for trajectory in test_trajectories:
        tactical_features = preprocessor.extract_tactical_features(trajectory)
        X_tactical.append(tactical_features)
        y_true.append(trajectory.tactical_mode)
    
    X_tactical = np.array(X_tactical)
    
    logger.info(f"分类器测试数据: {X_tactical.shape}, 标签数量: {len(y_true)}")
    
    # 8类战术任务
    class_names = [
        "巡逻任务", "预警探测", "电子侦察", "电子干扰",
        "对空攻击", "对地攻击", "空中格斗", "撤退规避"
    ]
    
    # 评估增强分类器
    if "Enhanced_Classifier" in models:
        try:
            start_time = time.time()
            predictions_proba = models["Enhanced_Classifier"].predict(X_tactical, verbose=0)
            prediction_time = (time.time() - start_time) * 1000
            
            y_pred = [class_names[np.argmax(pred)] for pred in predictions_proba]
            
            evaluator.evaluate_tactical_classification(
                y_true, y_pred, predictions_proba, class_names, "Enhanced_Classifier"
            )
            
            logger.info(f"增强分类器预测时间: {prediction_time:.2f}ms")
            
        except Exception as e:
            logger.error(f"增强分类器评估失败: {e}")


def main():
    """主评估函数"""
    logger.info("开始综合性能评估...")
    
    # 创建评估器
    evaluator = PerformanceEvaluator("evaluation_results")
    
    # 加载模型
    models = load_models()
    
    if not models:
        logger.error("没有找到训练好的模型，请先运行训练脚本")
        return
    
    logger.info(f"加载了 {len(models)} 个模型组件")
    
    # 生成测试数据
    test_trajectories = generate_test_data()
    
    # 评估LSTM模型
    evaluate_lstm_models(models, test_trajectories, evaluator)
    
    # 评估分类器
    evaluate_classifiers(models, test_trajectories, evaluator)
    
    # 模拟实时性能测试
    logger.info("模拟实时性能测试...")
    prediction_times = np.random.normal(50, 15, 100).tolist()  # 模拟预测时间
    processing_times = np.random.normal(20, 8, 100).tolist()   # 模拟处理时间
    
    evaluator.evaluate_realtime_performance(prediction_times, processing_times)
    
    # 生成综合报告
    report = evaluator.generate_comprehensive_report()
    
    # 生成性能分析图表
    evaluator.plot_performance_analysis()
    
    # 输出关键结果摘要
    logger.info("=== 综合评估结果摘要 ===")
    
    for key, result in evaluator.evaluation_results.items():
        if "trajectory" in key:
            pos_metrics = result["position_metrics"]
            logger.info(f"{result['model_name']} 航迹预测:")
            logger.info(f"  - 平均位置误差: {pos_metrics['mean_position_error']:.2f}m")
            logger.info(f"  - RMSE: {pos_metrics['rmse_position']:.2f}m")
            logger.info(f"  - ADE: {result['ade']:.2f}m, FDE: {result['fde']:.2f}m")
        
        elif "classification" in key:
            clf_report = result["classification_report"]
            logger.info(f"{result['model_name']} 战术分类:")
            logger.info(f"  - 整体准确率: {clf_report['accuracy']:.3f}")
            logger.info(f"  - 宏平均F1: {clf_report['macro avg']['f1-score']:.3f}")
        
        elif key == "realtime_performance":
            latency = result["latency_stats"]
            throughput = result["throughput_stats"]
            logger.info("实时性能:")
            logger.info(f"  - 平均延迟: {latency['mean_prediction_latency_ms']:.2f}ms")
            logger.info(f"  - P95延迟: {latency['p95_prediction_latency_ms']:.2f}ms")
            logger.info(f"  - 吞吐量: {throughput['predictions_per_second']:.2f} pred/s")
    
    logger.info("综合性能评估完成！")
    logger.info(f"详细报告保存在: {evaluator.save_path}")


if __name__ == "__main__":
    main()
