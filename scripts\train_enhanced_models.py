"""
增强模型训练脚本
训练支持22维特征和8类战术任务的增强模型
"""

import sys
import numpy as np
from pathlib import Path
import click

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.enhanced_data_generator import EnhancedDataGenerator
from src.data.preprocessor import DataPreprocessor
from src.models.enhanced_classifier import EnhancedTacticalClassifier
from config.model_config import lstm_config, classifier_config
from config.logging_config import setup_logging

logger = setup_logging()


def create_enhanced_lstm_model():
    """创建增强LSTM模型"""
    import tensorflow as tf
    from tensorflow import keras
    from tensorflow.keras import layers
    
    logger.info("构建增强LSTM模型...")
    
    # 输入层
    inputs = keras.Input(shape=(lstm_config.sequence_length, lstm_config.input_dim), name="enhanced_input")
    
    # 特征分组处理（根据实际27维特征）
    position_features = layers.Lambda(lambda x: x[:, :, :6], name="position_features")(inputs)
    motion_features = layers.Lambda(lambda x: x[:, :, 6:12], name="motion_features")(inputs)
    attitude_features = layers.Lambda(lambda x: x[:, :, 12:17], name="attitude_features")(inputs)
    radar_features = layers.Lambda(lambda x: x[:, :, 17:23], name="radar_features")(inputs)
    signal_features = layers.Lambda(lambda x: x[:, :, 23:], name="signal_features")(inputs)
    
    # 分别处理不同特征
    pos_lstm = layers.LSTM(64, return_sequences=True, name="pos_lstm")(position_features)
    motion_lstm = layers.LSTM(64, return_sequences=True, name="motion_lstm")(motion_features)
    attitude_lstm = layers.LSTM(32, return_sequences=True, name="attitude_lstm")(attitude_features)
    radar_lstm = layers.LSTM(32, return_sequences=True, name="radar_lstm")(radar_features)
    signal_lstm = layers.LSTM(16, return_sequences=True, name="signal_lstm")(signal_features)

    # 特征融合
    fused = layers.Concatenate(axis=-1, name="feature_fusion")([
        pos_lstm, motion_lstm, attitude_lstm, radar_lstm, signal_lstm
    ])
    
    # 主LSTM层
    x = layers.LSTM(lstm_config.hidden_dim, return_sequences=True, dropout=0.3, name="main_lstm_1")(fused)
    x = layers.BatchNormalization(name="bn_1")(x)
    x = layers.LSTM(lstm_config.hidden_dim, return_sequences=True, dropout=0.3, name="main_lstm_2")(x)
    x = layers.BatchNormalization(name="bn_2")(x)
    x = layers.LSTM(lstm_config.hidden_dim, return_sequences=False, dropout=0.3, name="main_lstm_3")(x)
    
    # 全连接层
    x = layers.Dense(lstm_config.hidden_dim, activation="relu", name="dense_1")(x)
    x = layers.BatchNormalization(name="bn_dense_1")(x)
    x = layers.Dropout(0.3)(x)
    
    x = layers.Dense(lstm_config.hidden_dim // 2, activation="relu", name="dense_2")(x)
    x = layers.Dropout(0.3)(x)
    
    # 输出层
    outputs = layers.Dense(
        lstm_config.output_dim * lstm_config.prediction_horizon,
        activation="linear",
        name="enhanced_output"
    )(x)
    
    outputs = layers.Reshape(
        (lstm_config.prediction_horizon, lstm_config.output_dim),
        name="reshape_output"
    )(outputs)
    
    # 创建模型
    model = keras.Model(inputs=inputs, outputs=outputs, name="Enhanced_LSTM_Model")
    
    # 自定义损失函数
    def enhanced_loss(y_true, y_pred):
        # 位置损失
        pos_loss = tf.reduce_mean(tf.square(y_true[:, :, :3] - y_pred[:, :, :3]))
        # 速度损失
        vel_loss = tf.reduce_mean(tf.square(y_true[:, :, 3:] - y_pred[:, :, 3:]))
        # 组合损失
        return pos_loss + 0.3 * vel_loss
    
    # 编译模型
    optimizer = keras.optimizers.AdamW(learning_rate=0.0005, weight_decay=0.01)
    model.compile(
        optimizer=optimizer,
        loss=enhanced_loss,
        metrics=["mae", "mse"]
    )
    
    logger.info(f"增强LSTM模型构建完成，参数量: {model.count_params()}")
    return model


@click.command()
@click.option('--samples-per-class', default=150, help='每类战术任务的样本数量')
@click.option('--sequence-length', default=50, help='序列长度')
@click.option('--model-save-path', default='data/models', help='模型保存路径')
def train_enhanced_models(samples_per_class: int, sequence_length: int, model_save_path: str):
    """训练增强模型"""
    
    logger.info("开始增强模型训练流程...")
    
    # 创建保存目录
    Path(model_save_path).mkdir(parents=True, exist_ok=True)
    
    # 1. 生成增强数据集
    logger.info("生成增强数据集...")
    generator = EnhancedDataGenerator()
    trajectories = generator.generate_balanced_dataset(
        samples_per_class=samples_per_class,
        sequence_length=sequence_length
    )
    
    # 2. 数据预处理
    logger.info("数据预处理...")
    preprocessor = DataPreprocessor()
    
    # 准备LSTM训练数据
    X_lstm, y_lstm = preprocessor.prepare_training_data(
        trajectories, 
        sequence_length=lstm_config.sequence_length,
        prediction_horizon=lstm_config.prediction_horizon
    )
    
    # 准备分类器训练数据
    X_classifier = []
    y_classifier = []
    
    for trajectory in trajectories:
        tactical_features = preprocessor.extract_tactical_features(trajectory)
        X_classifier.append(tactical_features)
        y_classifier.append(trajectory.tactical_mode)
    
    X_classifier = np.array(X_classifier)
    
    logger.info(f"LSTM数据形状: X{X_lstm.shape}, y{y_lstm.shape}")
    logger.info(f"分类器数据形状: X{X_classifier.shape}, 标签数量{len(y_classifier)}")
    
    # 3. 数据分割
    split_idx_lstm = int(len(X_lstm) * 0.7)
    split_idx_clf = int(len(X_classifier) * 0.7)
    
    X_lstm_train, X_lstm_val = X_lstm[:split_idx_lstm], X_lstm[split_idx_lstm:]
    y_lstm_train, y_lstm_val = y_lstm[:split_idx_lstm], y_lstm[split_idx_lstm:]
    
    X_clf_train, X_clf_val = X_classifier[:split_idx_clf], X_classifier[split_idx_clf:]
    y_clf_train, y_clf_val = y_classifier[:split_idx_clf], y_classifier[split_idx_clf:]
    
    # 4. 训练增强LSTM模型
    logger.info("训练增强LSTM模型...")
    import tensorflow as tf
    from tensorflow import keras

    lstm_model = create_enhanced_lstm_model()

    # LSTM训练回调
    lstm_callbacks = [
        keras.callbacks.EarlyStopping(monitor="val_loss", patience=15, restore_best_weights=True),
        keras.callbacks.ReduceLROnPlateau(monitor="val_loss", factor=0.5, patience=8, min_lr=1e-7),
        keras.callbacks.ModelCheckpoint(
            filepath=f"{model_save_path}/best_enhanced_lstm.h5",
            monitor="val_loss",
            save_best_only=True
        )
    ]
    
    lstm_history = lstm_model.fit(
        X_lstm_train, y_lstm_train,
        batch_size=32,
        epochs=50,
        validation_data=(X_lstm_val, y_lstm_val),
        callbacks=lstm_callbacks,
        verbose=1
    )
    
    # 保存LSTM模型
    lstm_model_path = Path(model_save_path) / "enhanced_lstm_model.h5"
    lstm_model.save(str(lstm_model_path))
    
    # 5. 训练增强分类器
    logger.info("训练增强战术分类器...")
    classifier = EnhancedTacticalClassifier(classifier_config)
    
    classifier_history = classifier.train(
        X_clf_train, y_clf_train,
        X_clf_val, y_clf_val
    )
    
    # 保存分类器
    classifier_model_path = Path(model_save_path) / "enhanced_classifier_model.h5"
    classifier.save_model(str(classifier_model_path))
    
    # 6. 模型评估
    logger.info("模型评估...")
    
    # LSTM评估
    lstm_metrics = lstm_model.evaluate(X_lstm_val, y_lstm_val, verbose=0)
    logger.info(f"增强LSTM性能 - Loss: {lstm_metrics[0]:.4f}, MAE: {lstm_metrics[1]:.4f}")
    
    # 分类器评估
    clf_metrics = classifier.model.evaluate(
        X_clf_val, 
        classifier.encode_labels(y_clf_val), 
        verbose=0
    )
    logger.info(f"增强分类器性能 - Loss: {clf_metrics[0]:.4f}, Accuracy: {clf_metrics[1]:.4f}")
    
    logger.info("增强模型训练完成！")
    logger.info(f"LSTM模型保存在: {lstm_model_path}")
    logger.info(f"分类器保存在: {classifier_model_path}")


if __name__ == "__main__":
    train_enhanced_models()
