"""
实时预测演示
演示实时航迹预测引擎的使用
"""

import sys
import time
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
import threading
from typing import Dict

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.prediction.real_time_engine import RealTimePredictionEngine
from src.data.radar_data import RadarSignal

# 设置日志
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RadarDataSimulator:
    """雷达数据模拟器"""
    
    def __init__(self, engine: RealTimePredictionEngine):
        self.engine = engine
        self.is_running = False
        self.simulation_thread = None
        
        # 模拟目标状态
        self.targets = {
            "target_001": {
                "position": [1000, 2000, 10000],
                "velocity": [150, 50, 0],
                "tactical_mode": "巡逻"
            },
            "target_002": {
                "position": [5000, 3000, 8000],
                "velocity": [-300, 100, -20],
                "tactical_mode": "攻击突击"
            }
        }
    
    def _simulate_data(self):
        """模拟数据生成"""
        logger.info("开始模拟雷达数据...")
        
        time_step = 0
        while self.is_running:
            current_time = datetime.now()
            
            for target_id, target_state in self.targets.items():
                # 更新目标位置
                dt = 1.0  # 时间步长（秒）
                for i in range(3):
                    target_state["position"][i] += target_state["velocity"][i] * dt
                
                # 添加噪声
                noise_pos = np.random.normal(0, 10, 3)
                noise_vel = np.random.normal(0, 5, 3)
                
                # 创建雷达信号
                signal = RadarSignal(
                    timestamp=current_time,
                    station_id="radar_001",
                    target_id=target_id,
                    toa=0.1 + time_step * 0.01 + np.random.normal(0, 0.001),
                    pa=np.pi/4 + time_step * 0.02 + np.random.normal(0, 0.01),
                    position=tuple(np.array(target_state["position"]) + noise_pos),
                    velocity=tuple(np.array(target_state["velocity"]) + noise_vel),
                    heading=np.random.uniform(0, 2*np.pi),
                    elevation=np.random.uniform(-np.pi/6, np.pi/6),
                    pitch=np.random.uniform(-0.2, 0.2),
                    roll=np.random.uniform(-0.1, 0.1),
                    snr=np.random.uniform(15, 25),
                    confidence=np.random.uniform(0.8, 0.95)
                )
                
                # 发送信号到预测引擎
                self.engine.add_radar_signal(signal)
            
            time_step += 1
            time.sleep(1)  # 每秒生成一次数据
    
    def start(self):
        """启动数据模拟"""
        if self.is_running:
            return
        
        self.is_running = True
        self.simulation_thread = threading.Thread(target=self._simulate_data, daemon=True)
        self.simulation_thread.start()
        logger.info("雷达数据模拟器已启动")
    
    def stop(self):
        """停止数据模拟"""
        self.is_running = False
        if self.simulation_thread:
            self.simulation_thread.join(timeout=2)
        logger.info("雷达数据模拟器已停止")


def prediction_callback(predictions: Dict):
    """预测结果回调函数"""
    if not predictions:
        return
    
    logger.info("=== 实时预测结果 ===")
    for target_id, result in predictions.items():
        logger.info(f"目标: {target_id}")
        logger.info(f"  当前位置: {result['last_position']}")
        logger.info(f"  当前速度: {result['last_velocity']}")
        logger.info(f"  数据点数: {result['data_points']}")
        
        if result.get('tactical_prediction'):
            tactical = result['tactical_prediction']
            logger.info(f"  战术模式: {tactical['predicted_mode']} (置信度: {tactical['confidence']:.3f})")
        
        if result.get('trajectory_prediction'):
            traj = np.array(result['trajectory_prediction'])
            logger.info(f"  预测轨迹: 未来{len(traj)}个时间步")
            logger.info(f"  预测终点: {traj[-1]}")
        
        logger.info("")


def main():
    """主演示函数"""
    logger.info("开始实时预测演示...")
    
    # 检查模型文件
    lstm_model_path = "data/models/lstm_trajectory_model.h5"
    classifier_model_path = "data/models/tactical_classifier_model.h5"
    
    if not Path(lstm_model_path).exists():
        logger.error(f"LSTM模型不存在: {lstm_model_path}")
        logger.info("请先运行: python scripts/simple_train.py")
        return
    
    if not Path(classifier_model_path).exists():
        logger.error(f"分类器模型不存在: {classifier_model_path}")
        logger.info("请先运行: python scripts/train_classifier.py")
        return
    
    # 创建实时预测引擎
    engine = RealTimePredictionEngine(
        lstm_model_path=lstm_model_path,
        classifier_model_path=classifier_model_path,
        prediction_interval=2.0  # 每2秒预测一次
    )
    
    # 添加预测结果回调
    engine.add_prediction_callback(prediction_callback)
    
    # 创建数据模拟器
    simulator = RadarDataSimulator(engine)
    
    try:
        # 启动引擎和模拟器
        engine.start()
        simulator.start()
        
        logger.info("实时预测演示运行中...")
        logger.info("按 Ctrl+C 停止演示")
        
        # 运行30秒演示
        for i in range(30):
            time.sleep(1)
            if i % 5 == 0:
                # 每5秒显示目标状态
                status = engine.get_target_status()
                logger.info(f"--- 第{i+1}秒状态 ---")
                for target_id, info in status.items():
                    logger.info(f"{target_id}: {info['data_points']}个数据点, "
                               f"预测状态: {'有' if info['has_prediction'] else '无'}")
        
    except KeyboardInterrupt:
        logger.info("用户中断演示")
    
    finally:
        # 停止引擎和模拟器
        simulator.stop()
        engine.stop()
        logger.info("实时预测演示结束")


if __name__ == "__main__":
    main()
