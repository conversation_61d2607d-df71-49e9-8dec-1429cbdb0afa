"""
增强模型完整演示
展示22维特征输入、8类战术识别、实时预测等所有增强功能
"""

import sys
import numpy as np
import time
from pathlib import Path
from datetime import datetime, timedelta
from typing import List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.radar_data import RadarSignal, TargetTrajectory
from src.data.enhanced_data_generator import EnhancedDataGenerator
from src.data.preprocessor import DataPreprocessor
import tensorflow as tf

# 设置日志
import logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s | %(levelname)s | %(message)s')
logger = logging.getLogger(__name__)


def demonstrate_enhanced_features():
    """演示增强特征功能"""
    logger.info("=== 增强特征演示 ===")
    
    # 创建一个增强雷达信号
    enhanced_signal = RadarSignal(
        timestamp=datetime.now(),
        station_id="demo_radar",
        target_id="demo_target",
        toa=0.15,
        pa=0.785,
        
        # 位置信息（增强）
        position=(5000.0, 3000.0, 8000.0),
        position_accuracy=(10.0, 12.0, 20.0),
        
        # 运动参数（增强）
        velocity=(250.0, 150.0, 30.0),
        acceleration=(5.0, -2.0, 1.0),
        
        # 姿态信息（完整）
        heading=0.524,
        elevation=0.118,
        pitch=0.1,
        roll=-0.05,
        yaw=0.02,
        
        # 雷达测量参数（新增）
        target_distance=9434.0,
        pulse_width=1.2,
        radar_frequency=3000.0,
        
        # 信号特征（新增）
        prf=2500.0,
        signal_strength=0.85,
        communication_status=0,
        
        # 信号质量
        snr=20.5,
        confidence=0.92
    )
    
    # 展示特征向量
    feature_vector = enhanced_signal.to_feature_vector()
    logger.info(f"增强特征向量维度: {len(feature_vector)}")
    logger.info(f"特征向量: {feature_vector}")
    
    # 展示分组特征
    logger.info(f"位置特征: {enhanced_signal.get_position_features()}")
    logger.info(f"运动特征: {enhanced_signal.get_motion_features()}")
    logger.info(f"姿态特征: {enhanced_signal.get_attitude_features()}")
    logger.info(f"雷达特征: {enhanced_signal.get_radar_features()}")


def demonstrate_8class_classification():
    """演示8类战术任务识别"""
    logger.info("\n=== 8类战术任务识别演示 ===")
    
    # 检查分类器模型
    classifier_path = "data/models/enhanced_tactical_classifier.h5"
    labels_path = "data/models/enhanced_tactical_classifier.labels.npy"
    
    if not Path(classifier_path).exists():
        logger.error("增强分类器模型不存在，请先运行训练脚本")
        return
    
    try:
        # 加载模型和标签
        classifier = tf.keras.models.load_model(classifier_path)
        
        if Path(labels_path).exists():
            label_encoder = np.load(labels_path, allow_pickle=True).item()
            reverse_labels = {v: k for k, v in label_encoder.items()}
        else:
            reverse_labels = {
                0: "巡逻任务", 1: "预警探测", 2: "电子侦察", 3: "电子干扰",
                4: "对空攻击", 5: "对地攻击", 6: "空中格斗", 7: "撤退规避"
            }
        
        logger.info(f"支持的8类战术任务: {list(reverse_labels.values())}")
        
        # 生成测试样本
        generator = EnhancedDataGenerator()
        
        for tactical_mode in reverse_labels.values():
            logger.info(f"\n测试 {tactical_mode}:")
            
            # 生成该战术模式的航迹
            trajectory = generator.generate_tactical_trajectory(
                tactical_mode=tactical_mode,
                sequence_length=30,
                target_id=f"test_{tactical_mode}"
            )
            
            # 提取战术特征
            preprocessor = DataPreprocessor()
            tactical_features = preprocessor.extract_tactical_features(trajectory)
            
            # 预测
            start_time = time.time()
            prediction = classifier.predict(tactical_features.reshape(1, -1), verbose=0)
            prediction_time = (time.time() - start_time) * 1000
            
            # 分析结果
            predicted_class = np.argmax(prediction[0])
            confidence = prediction[0][predicted_class]
            predicted_mode = reverse_labels[predicted_class]
            
            # 显示所有类别的概率
            logger.info(f"  预测结果: {predicted_mode} (置信度: {confidence:.3f})")
            logger.info(f"  预测时间: {prediction_time:.2f}ms")
            logger.info(f"  正确性: {'✓' if predicted_mode == tactical_mode else '✗'}")
            
            # 显示概率分布
            prob_str = ", ".join([f"{reverse_labels[i]}:{prediction[0][i]:.3f}" 
                                for i in range(len(reverse_labels))])
            logger.info(f"  概率分布: {prob_str}")
    
    except Exception as e:
        logger.error(f"8类战术识别演示失败: {e}")


def demonstrate_realtime_prediction():
    """演示实时预测功能"""
    logger.info("\n=== 实时预测演示 ===")
    
    # 检查LSTM模型
    lstm_path = "data/models/lstm_trajectory_model.h5"
    
    if not Path(lstm_path).exists():
        logger.error("LSTM模型不存在，请先运行训练脚本")
        return
    
    try:
        # 加载LSTM模型
        lstm_model = tf.keras.models.load_model(lstm_path)
        logger.info(f"LSTM模型输入形状: {lstm_model.input_shape}")
        logger.info(f"LSTM模型输出形状: {lstm_model.output_shape}")
        
        # 生成实时数据流
        generator = EnhancedDataGenerator()
        trajectory = generator.generate_tactical_trajectory(
            tactical_mode="空中格斗",
            sequence_length=50,
            target_id="realtime_demo"
        )
        
        logger.info("模拟实时数据流...")
        
        # 模拟实时预测
        window_size = 20
        prediction_times = []
        
        for i in range(window_size, len(trajectory.signals) - 10, 5):
            # 获取历史数据窗口
            window_signals = trajectory.signals[i-window_size:i]
            
            # 准备LSTM输入（6维基础特征）
            lstm_input = np.zeros((1, window_size, 6))
            for j, signal in enumerate(window_signals):
                lstm_input[0, j, :] = [
                    signal.position[0], signal.position[1], signal.position[2],
                    signal.velocity[0], signal.velocity[1], signal.velocity[2]
                ]
            
            # 实时预测
            start_time = time.time()
            prediction = lstm_model.predict(lstm_input, verbose=0)
            prediction_time = (time.time() - start_time) * 1000
            prediction_times.append(prediction_time)
            
            # 获取真实未来位置（用于对比）
            future_signals = trajectory.signals[i:i+10]
            true_positions = [s.position for s in future_signals]
            
            # 计算预测误差
            pred_positions = prediction[0, :len(true_positions), :]
            errors = []
            for k in range(len(true_positions)):
                error = np.linalg.norm(np.array(true_positions[k]) - pred_positions[k])
                errors.append(error)
            
            avg_error = np.mean(errors)
            
            logger.info(f"  时刻 {i}: 预测误差 {avg_error:.1f}m, 预测时间 {prediction_time:.2f}ms")
        
        # 实时性能统计
        avg_prediction_time = np.mean(prediction_times)
        max_prediction_time = np.max(prediction_times)
        p95_prediction_time = np.percentile(prediction_times, 95)
        
        logger.info(f"\n实时预测性能统计:")
        logger.info(f"  - 平均预测时间: {avg_prediction_time:.2f}ms")
        logger.info(f"  - 最大预测时间: {max_prediction_time:.2f}ms")
        logger.info(f"  - P95预测时间: {p95_prediction_time:.2f}ms")
        logger.info(f"  - 预测频率: {1000/avg_prediction_time:.1f} Hz")
        
    except Exception as e:
        logger.error(f"实时预测演示失败: {e}")


def demonstrate_combat_scenarios():
    """演示实战场景应用"""
    logger.info("\n=== 实战场景应用演示 ===")
    
    # 模拟一个复杂的空战场景
    base_time = datetime.now()
    
    # 创建多个目标的复杂场景
    scenario_targets = [
        {
            "id": "预警机_001",
            "mode": "预警探测", 
            "description": "高空预警探测，提供战场态势感知"
        },
        {
            "id": "电子战_001", 
            "mode": "电子干扰",
            "description": "电子干扰机，压制敌方雷达"
        },
        {
            "id": "攻击机_001",
            "mode": "对地攻击", 
            "description": "低空突防，执行对地攻击任务"
        },
        {
            "id": "护航机_001",
            "mode": "空中格斗",
            "description": "护航战斗机，准备空中格斗"
        }
    ]
    
    logger.info("复杂空战场景:")
    for target in scenario_targets:
        logger.info(f"  - {target['id']}: {target['mode']} ({target['description']})")
    
    # 检查模型可用性
    classifier_path = "data/models/enhanced_tactical_classifier.h5"
    
    if Path(classifier_path).exists():
        try:
            classifier = tf.keras.models.load_model(classifier_path)
            
            # 为每个目标生成数据并预测
            generator = EnhancedDataGenerator()
            preprocessor = DataPreprocessor()
            
            logger.info("\n实时战术识别结果:")
            
            for target in scenario_targets:
                # 生成航迹
                trajectory = generator.generate_tactical_trajectory(
                    tactical_mode=target["mode"],
                    sequence_length=25,
                    target_id=target["id"]
                )
                
                # 提取特征并预测
                tactical_features = preprocessor.extract_tactical_features(trajectory)
                prediction = classifier.predict(tactical_features.reshape(1, -1), verbose=0)
                
                predicted_class = np.argmax(prediction[0])
                confidence = prediction[0][predicted_class]
                
                # 8类标签
                class_names = [
                    "巡逻任务", "预警探测", "电子侦察", "电子干扰",
                    "对空攻击", "对地攻击", "空中格斗", "撤退规避"
                ]
                
                predicted_mode = class_names[predicted_class]
                
                status = "✓ 正确" if predicted_mode == target["mode"] else "✗ 错误"
                
                logger.info(f"  {target['id']}: {target['mode']} -> {predicted_mode} "
                           f"(置信度:{confidence:.3f}) {status}")
        
        except Exception as e:
            logger.error(f"实战场景演示失败: {e}")
    else:
        logger.warning("增强分类器模型不存在，跳过实战场景演示")


def main():
    """主演示函数"""
    logger.info("🚀 增强目标航迹ZZ跟踪实时预测模型 - 完整功能演示")
    logger.info("=" * 60)
    
    # 1. 增强特征演示
    demonstrate_enhanced_features()
    
    # 2. 8类战术任务识别演示
    demonstrate_8class_classification()
    
    # 3. 实时预测演示
    demonstrate_realtime_prediction()
    
    # 4. 实战场景应用演示
    demonstrate_combat_scenarios()
    
    logger.info("\n" + "=" * 60)
    logger.info("🎉 增强模型功能演示完成！")
    
    # 总结增强功能
    logger.info("\n📋 增强功能总结:")
    logger.info("✅ 22维增强特征输入 - 支持位置精度、加速度、完整姿态、雷达参数、信号特征")
    logger.info("✅ 8类战术任务识别 - 巡逻、预警、电侦、电干、对空攻击、对地攻击、格斗、撤退")
    logger.info("✅ 增强LSTM网络 - 特征分组处理、注意力机制、批归一化")
    logger.info("✅ 高级训练策略 - Focal Loss、类别权重、标签平滑、数据增强")
    logger.info("✅ 实时性能优化 - 毫秒级预测响应、多线程处理")
    logger.info("✅ 综合性能评估 - 完整评估指标、实战场景测试")
    logger.info("✅ 可视化分析 - 3D航迹、性能图表、混淆矩阵")
    
    logger.info("\n🎯 实战应用能力:")
    logger.info("• 多目标同时跟踪预测")
    logger.info("• 复杂电子对抗环境适应")
    logger.info("• 高机动目标航迹预测")
    logger.info("• 实时战术意图识别")
    logger.info("• 不确定性量化评估")
    
    logger.info("\n📊 性能指标:")
    logger.info("• 预测延迟: <1秒")
    logger.info("• 分类准确率: 实战场景33.3%")
    logger.info("• 支持特征: 22维完整特征")
    logger.info("• 战术类别: 8类军事任务")
    
    logger.info("\n🔧 使用建议:")
    logger.info("1. 使用真实雷达数据进行模型微调")
    logger.info("2. 根据具体应用场景调整特征权重")
    logger.info("3. 定期重训练以适应新的战术模式")
    logger.info("4. 结合领域专家知识优化特征工程")
    
    logger.info("\n📁 相关文件:")
    logger.info("• 增强模型训练: scripts/train_enhanced_simple.py")
    logger.info("• 性能评估: examples/simple_evaluation.py")
    logger.info("• 实战测试: examples/combat_scenario_test.py")
    logger.info("• 完整文档: docs/ENHANCED_MODEL_GUIDE.md")


if __name__ == "__main__":
    main()
