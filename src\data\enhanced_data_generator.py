"""
增强数据生成器
生成8类战术任务的模拟雷达数据
"""

import numpy as np
from datetime import datetime, timedelta
from typing import List, Tuple
import math

from .radar_data import RadarSignal, TargetTrajectory
from config.logging_config import default_logger as logger


class EnhancedDataGenerator:
    """增强数据生成器"""
    
    def __init__(self):
        self.tactical_modes = [
            "巡逻任务", "预警探测", "电子侦察", "电子干扰",
            "对空攻击", "对地攻击", "空中格斗", "撤退规避"
        ]
    
    def generate_tactical_trajectory(self, tactical_mode: str, 
                                   sequence_length: int = 50,
                                   target_id: str = "target_001") -> TargetTrajectory:
        """
        根据战术模式生成特定的航迹数据
        
        Args:
            tactical_mode: 战术模式
            sequence_length: 序列长度
            target_id: 目标ID
            
        Returns:
            目标航迹
        """
        signals = []
        base_time = datetime.now()
        
        # 根据战术模式设置初始参数
        if tactical_mode == "巡逻任务":
            # 巡逻：稳定速度，规律航线，中等高度
            base_pos = [2000, 3000, 10000]
            base_vel = [150, 50, 0]
            speed_var = 20
            altitude_var = 100
            maneuver_freq = 0.1
            
        elif tactical_mode == "预警探测":
            # 预警：高空，慢速，大范围扫描
            base_pos = [5000, 5000, 15000]
            base_vel = [100, 80, 10]
            speed_var = 15
            altitude_var = 200
            maneuver_freq = 0.05
            
        elif tactical_mode == "电子侦察":
            # 电子侦察：中高空，中速，复杂航线
            base_pos = [8000, 4000, 12000]
            base_vel = [200, 120, 5]
            speed_var = 30
            altitude_var = 300
            maneuver_freq = 0.15
            
        elif tactical_mode == "电子干扰":
            # 电子干扰：特定位置，悬停或慢速移动
            base_pos = [6000, 6000, 11000]
            base_vel = [50, 30, 0]
            speed_var = 25
            altitude_var = 150
            maneuver_freq = 0.08
            
        elif tactical_mode == "对空攻击":
            # 对空攻击：高速，直线接近，快速爬升
            base_pos = [10000, 2000, 8000]
            base_vel = [-400, 200, 100]
            speed_var = 50
            altitude_var = 500
            maneuver_freq = 0.3
            
        elif tactical_mode == "对地攻击":
            # 对地攻击：中速，低空，俯冲特征
            base_pos = [7000, 8000, 5000]
            base_vel = [250, -150, -50]
            speed_var = 40
            altitude_var = 200
            maneuver_freq = 0.2
            
        elif tactical_mode == "空中格斗":
            # 空中格斗：高机动，频繁变向，中等高度
            base_pos = [4000, 4000, 8000]
            base_vel = [300, 200, 50]
            speed_var = 100
            altitude_var = 800
            maneuver_freq = 0.5
            
        else:  # 撤退规避
            # 撤退：高速，直线远离，快速爬升
            base_pos = [3000, 7000, 6000]
            base_vel = [500, -300, 150]
            speed_var = 80
            altitude_var = 600
            maneuver_freq = 0.4
        
        # 生成航迹点
        current_pos = np.array(base_pos, dtype=float)
        current_vel = np.array(base_vel, dtype=float)
        
        for t in range(sequence_length):
            # 时间步长
            dt = 1.0
            
            # 根据战术模式添加机动
            if np.random.random() < maneuver_freq:
                # 执行机动
                maneuver_intensity = np.random.uniform(0.5, 2.0)
                vel_change = np.random.normal(0, speed_var * maneuver_intensity, 3)
                current_vel += vel_change
            
            # 更新位置
            current_pos += current_vel * dt
            
            # 添加噪声
            pos_noise = np.random.normal(0, 10, 3)
            vel_noise = np.random.normal(0, 5, 3)
            
            # 计算加速度（简化）
            if t > 0:
                acceleration = (current_vel - prev_vel) / dt
            else:
                acceleration = np.array([0.0, 0.0, 0.0])
            
            # 计算姿态角
            speed = np.linalg.norm(current_vel)
            heading = math.atan2(current_vel[1], current_vel[0])
            elevation = math.atan2(current_vel[2], math.sqrt(current_vel[0]**2 + current_vel[1]**2))
            
            # 根据战术模式调整姿态
            if tactical_mode == "空中格斗":
                pitch = np.random.uniform(-0.5, 0.5)
                roll = np.random.uniform(-0.8, 0.8)
                yaw = np.random.uniform(-0.3, 0.3)
            elif tactical_mode in ["对空攻击", "对地攻击"]:
                pitch = np.random.uniform(-0.3, 0.3)
                roll = np.random.uniform(-0.2, 0.2)
                yaw = np.random.uniform(-0.1, 0.1)
            else:
                pitch = np.random.uniform(-0.1, 0.1)
                roll = np.random.uniform(-0.1, 0.1)
                yaw = np.random.uniform(-0.05, 0.05)
            
            # 雷达参数（根据战术模式调整）
            if tactical_mode in ["电子侦察", "电子干扰"]:
                # 电子战任务可能影响雷达参数
                communication_status = np.random.choice([0, 1, 2], p=[0.6, 0.3, 0.1])
                signal_strength = np.random.uniform(0.3, 0.8)
            else:
                communication_status = np.random.choice([0, 1], p=[0.9, 0.1])
                signal_strength = np.random.uniform(0.7, 1.0)
            
            # 创建雷达信号
            signal = RadarSignal(
                timestamp=base_time + timedelta(seconds=t),
                station_id="radar_001",
                target_id=target_id,
                toa=0.1 + t * 0.01 + np.random.normal(0, 0.001),
                pa=np.pi/4 + t * 0.02 + np.random.normal(0, 0.01),
                
                # 位置信息
                position=tuple(current_pos + pos_noise),
                position_accuracy=(
                    np.random.uniform(5, 15),
                    np.random.uniform(5, 15), 
                    np.random.uniform(10, 30)
                ),
                
                # 运动参数
                velocity=tuple(current_vel + vel_noise),
                acceleration=tuple(acceleration),
                
                # 姿态信息
                heading=heading,
                elevation=elevation,
                pitch=pitch,
                roll=roll,
                yaw=yaw,
                
                # 雷达测量参数
                target_distance=np.linalg.norm(current_pos),
                pulse_width=np.random.uniform(0.5, 2.0),
                radar_frequency=np.random.uniform(2800, 3200),
                
                # 信号特征
                prf=np.random.uniform(1000, 5000),
                signal_strength=signal_strength,
                communication_status=communication_status,
                
                # 信号质量
                snr=np.random.uniform(15, 25),
                confidence=np.random.uniform(0.8, 0.95)
            )
            
            signals.append(signal)
            prev_vel = current_vel.copy()
        
        # 创建航迹
        trajectory = TargetTrajectory(
            target_id=target_id,
            target_type="战斗机",
            signals=signals,
            tactical_mode=tactical_mode
        )
        
        return trajectory
    
    def generate_balanced_dataset(self, samples_per_class: int = 100,
                                sequence_length: int = 50) -> List[TargetTrajectory]:
        """
        生成平衡的8类战术任务数据集
        
        Args:
            samples_per_class: 每类样本数量
            sequence_length: 序列长度
            
        Returns:
            平衡的航迹数据集
        """
        logger.info(f"生成平衡数据集，每类{samples_per_class}个样本...")
        
        trajectories = []
        
        for mode in self.tactical_modes:
            logger.info(f"生成 {mode} 数据...")
            
            for i in range(samples_per_class):
                target_id = f"{mode}_{i:03d}"
                trajectory = self.generate_tactical_trajectory(
                    tactical_mode=mode,
                    sequence_length=sequence_length,
                    target_id=target_id
                )
                trajectories.append(trajectory)
        
        logger.info(f"数据集生成完成，总计 {len(trajectories)} 个航迹")
        return trajectories
