"""
评估指标计算模块
定义航迹预测和分类任务的各种评估指标
"""

import numpy as np
from typing import List, Dict, Tuple
from sklearn.metrics import classification_report, confusion_matrix, f1_score
import math

from config.logging_config import default_logger as logger


class TrajectoryMetrics:
    """航迹预测评估指标"""
    
    @staticmethod
    def position_error(y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
        """
        计算位置预测误差
        
        Args:
            y_true: 真实位置 (batch_size, time_steps, 3)
            y_pred: 预测位置 (batch_size, time_steps, 3)
            
        Returns:
            位置误差指标字典
        """
        # 计算欧几里得距离误差
        position_errors = np.linalg.norm(y_true - y_pred, axis=-1)
        
        return {
            "mean_position_error": float(np.mean(position_errors)),
            "std_position_error": float(np.std(position_errors)),
            "max_position_error": float(np.max(position_errors)),
            "min_position_error": float(np.min(position_errors)),
            "median_position_error": float(np.median(position_errors)),
            "rmse_position": float(np.sqrt(np.mean(position_errors**2)))
        }
    
    @staticmethod
    def velocity_error(y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
        """
        计算速度预测误差
        
        Args:
            y_true: 真实速度 (batch_size, time_steps, 3)
            y_pred: 预测速度 (batch_size, time_steps, 3)
            
        Returns:
            速度误差指标字典
        """
        velocity_errors = np.linalg.norm(y_true - y_pred, axis=-1)
        
        return {
            "mean_velocity_error": float(np.mean(velocity_errors)),
            "std_velocity_error": float(np.std(velocity_errors)),
            "max_velocity_error": float(np.max(velocity_errors)),
            "rmse_velocity": float(np.sqrt(np.mean(velocity_errors**2)))
        }
    
    @staticmethod
    def temporal_consistency(predictions: np.ndarray) -> float:
        """
        计算时间一致性指标
        
        Args:
            predictions: 预测序列 (batch_size, time_steps, features)
            
        Returns:
            时间一致性分数
        """
        # 计算相邻时间步的变化率
        temporal_changes = np.diff(predictions, axis=1)
        change_magnitudes = np.linalg.norm(temporal_changes, axis=-1)
        
        # 一致性 = 1 / (1 + 变化率方差)
        consistency = 1.0 / (1.0 + np.var(change_magnitudes))
        return float(consistency)
    
    @staticmethod
    def prediction_horizon_analysis(y_true: np.ndarray, y_pred: np.ndarray) -> Dict[int, float]:
        """
        分析不同预测时间步长的误差
        
        Args:
            y_true: 真实值
            y_pred: 预测值
            
        Returns:
            各时间步的误差字典
        """
        horizon_errors = {}
        
        for t in range(y_true.shape[1]):
            errors = np.linalg.norm(y_true[:, t, :] - y_pred[:, t, :], axis=-1)
            horizon_errors[t + 1] = float(np.mean(errors))
        
        return horizon_errors
    
    @staticmethod
    def calculate_ade_fde(y_true: np.ndarray, y_pred: np.ndarray) -> Tuple[float, float]:
        """
        计算ADE (Average Displacement Error) 和 FDE (Final Displacement Error)
        
        Args:
            y_true: 真实轨迹
            y_pred: 预测轨迹
            
        Returns:
            (ADE, FDE)
        """
        # ADE: 所有时间步的平均位移误差
        displacement_errors = np.linalg.norm(y_true - y_pred, axis=-1)
        ade = float(np.mean(displacement_errors))
        
        # FDE: 最终时间步的位移误差
        final_errors = np.linalg.norm(y_true[:, -1, :] - y_pred[:, -1, :], axis=-1)
        fde = float(np.mean(final_errors))
        
        return ade, fde


class ClassificationMetrics:
    """分类任务评估指标"""
    
    @staticmethod
    def detailed_classification_report(y_true: List[str], y_pred: List[str], 
                                     class_names: List[str]) -> Dict:
        """
        生成详细的分类报告
        
        Args:
            y_true: 真实标签
            y_pred: 预测标签
            class_names: 类别名称列表
            
        Returns:
            详细分类报告
        """
        # 生成分类报告
        report = classification_report(y_true, y_pred, target_names=class_names, output_dict=True)
        
        # 计算混淆矩阵
        cm = confusion_matrix(y_true, y_pred, labels=class_names)
        
        # 计算各类别的F1分数
        f1_scores = f1_score(y_true, y_pred, labels=class_names, average=None)
        
        return {
            "classification_report": report,
            "confusion_matrix": cm.tolist(),
            "f1_scores": {class_names[i]: float(score) for i, score in enumerate(f1_scores)},
            "macro_f1": float(f1_score(y_true, y_pred, average='macro')),
            "weighted_f1": float(f1_score(y_true, y_pred, average='weighted'))
        }
    
    @staticmethod
    def confidence_calibration(y_true: np.ndarray, y_pred_proba: np.ndarray, 
                             num_bins: int = 10) -> Dict[str, float]:
        """
        计算置信度校准指标
        
        Args:
            y_true: 真实标签 (one-hot)
            y_pred_proba: 预测概率
            num_bins: 校准分箱数量
            
        Returns:
            校准指标
        """
        # 获取预测置信度和准确性
        confidences = np.max(y_pred_proba, axis=1)
        predictions = np.argmax(y_pred_proba, axis=1)
        true_labels = np.argmax(y_true, axis=1)
        accuracies = (predictions == true_labels).astype(float)
        
        # 分箱计算ECE (Expected Calibration Error)
        bin_boundaries = np.linspace(0, 1, num_bins + 1)
        bin_lowers = bin_boundaries[:-1]
        bin_uppers = bin_boundaries[1:]
        
        ece = 0
        for bin_lower, bin_upper in zip(bin_lowers, bin_uppers):
            in_bin = (confidences > bin_lower) & (confidences <= bin_upper)
            prop_in_bin = in_bin.mean()
            
            if prop_in_bin > 0:
                accuracy_in_bin = accuracies[in_bin].mean()
                avg_confidence_in_bin = confidences[in_bin].mean()
                ece += np.abs(avg_confidence_in_bin - accuracy_in_bin) * prop_in_bin
        
        return {
            "expected_calibration_error": float(ece),
            "average_confidence": float(np.mean(confidences)),
            "average_accuracy": float(np.mean(accuracies))
        }
    
    @staticmethod
    def tactical_transition_analysis(y_true: List[str], y_pred: List[str]) -> Dict:
        """
        分析战术模式转换的预测准确性
        
        Args:
            y_true: 真实战术序列
            y_pred: 预测战术序列
            
        Returns:
            转换分析结果
        """
        if len(y_true) != len(y_pred) or len(y_true) < 2:
            return {"error": "数据长度不足或不匹配"}
        
        # 检测转换点
        true_transitions = []
        pred_transitions = []
        
        for i in range(1, len(y_true)):
            if y_true[i] != y_true[i-1]:
                true_transitions.append(i)
            if y_pred[i] != y_pred[i-1]:
                pred_transitions.append(i)
        
        # 计算转换检测准确率
        transition_detection_accuracy = 0.0
        if len(true_transitions) > 0:
            detected_transitions = 0
            for true_t in true_transitions:
                # 允许±2个时间步的误差
                if any(abs(pred_t - true_t) <= 2 for pred_t in pred_transitions):
                    detected_transitions += 1
            
            transition_detection_accuracy = detected_transitions / len(true_transitions)
        
        return {
            "true_transitions": len(true_transitions),
            "predicted_transitions": len(pred_transitions),
            "transition_detection_accuracy": float(transition_detection_accuracy),
            "false_positive_transitions": max(0, len(pred_transitions) - len(true_transitions))
        }


class RealTimeMetrics:
    """实时性能指标"""
    
    def __init__(self):
        self.prediction_times = []
        self.processing_times = []
        self.memory_usage = []
    
    def record_prediction_time(self, time_ms: float):
        """记录预测时间"""
        self.prediction_times.append(time_ms)
    
    def record_processing_time(self, time_ms: float):
        """记录数据处理时间"""
        self.processing_times.append(time_ms)
    
    def get_latency_stats(self) -> Dict[str, float]:
        """获取延迟统计"""
        if not self.prediction_times:
            return {"error": "无预测时间数据"}
        
        pred_times = np.array(self.prediction_times)
        
        return {
            "mean_prediction_latency_ms": float(np.mean(pred_times)),
            "p95_prediction_latency_ms": float(np.percentile(pred_times, 95)),
            "p99_prediction_latency_ms": float(np.percentile(pred_times, 99)),
            "max_prediction_latency_ms": float(np.max(pred_times)),
            "total_predictions": len(pred_times)
        }
    
    def get_throughput_stats(self, time_window_seconds: float) -> Dict[str, float]:
        """获取吞吐量统计"""
        if not self.prediction_times:
            return {"error": "无数据"}
        
        predictions_per_second = len(self.prediction_times) / time_window_seconds
        
        return {
            "predictions_per_second": float(predictions_per_second),
            "time_window_seconds": time_window_seconds,
            "total_predictions": len(self.prediction_times)
        }
