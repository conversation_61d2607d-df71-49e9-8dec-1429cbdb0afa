"""
简化性能评估
快速评估模型性能
"""

import sys
import numpy as np
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import tensorflow as tf

# 设置日志
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def evaluate_models():
    """评估现有模型"""
    logger.info("开始模型性能评估...")
    
    # 检查可用模型
    model_files = [
        ("基础LSTM", "data/models/lstm_trajectory_model.h5"),
        ("增强分类器", "data/models/enhanced_tactical_classifier.h5"),
        ("基础分类器", "data/models/tactical_classifier_model.h5")
    ]
    
    results = {}
    
    for model_name, model_path in model_files:
        if not Path(model_path).exists():
            logger.warning(f"{model_name} 模型文件不存在: {model_path}")
            continue
        
        try:
            logger.info(f"评估 {model_name}...")
            
            # 加载模型
            model = tf.keras.models.load_model(model_path)
            
            # 获取模型信息
            input_shape = model.input_shape
            output_shape = model.output_shape
            param_count = model.count_params()
            
            logger.info(f"  模型结构: 输入{input_shape} -> 输出{output_shape}")
            logger.info(f"  参数量: {param_count:,}")
            
            # 生成测试数据
            if "lstm" in model_path.lower():
                # LSTM模型测试
                if "enhanced" in model_path:
                    test_input = np.random.randn(10, 20, 27)  # 增强特征
                else:
                    test_input = np.random.randn(10, 20, 6)   # 基础特征
                
                # 预测性能测试
                start_time = time.time()
                predictions = model.predict(test_input, verbose=0)
                prediction_time = (time.time() - start_time) * 1000
                
                logger.info(f"  预测时间: {prediction_time:.2f}ms")
                logger.info(f"  预测形状: {predictions.shape}")
                
                # 计算简单的一致性指标
                pred_changes = np.diff(predictions, axis=1)
                consistency = 1.0 / (1.0 + np.var(np.linalg.norm(pred_changes, axis=-1)))
                logger.info(f"  时间一致性: {consistency:.3f}")
                
                results[model_name] = {
                    "type": "trajectory_prediction",
                    "input_shape": input_shape,
                    "output_shape": output_shape,
                    "parameters": param_count,
                    "prediction_time_ms": prediction_time,
                    "temporal_consistency": consistency
                }
            
            else:
                # 分类器测试
                if "enhanced" in model_path:
                    test_input = np.random.randn(100, 15)  # 15维战术特征
                    num_classes = 8
                    class_names = [
                        "巡逻任务", "预警探测", "电子侦察", "电子干扰",
                        "对空攻击", "对地攻击", "空中格斗", "撤退规避"
                    ]
                else:
                    test_input = np.random.randn(100, 10)  # 10维战术特征
                    num_classes = 6
                    class_names = ["巡逻", "预警探测", "攻击突击", "空中格斗", "对抗", "其他"]
                
                # 预测性能测试
                start_time = time.time()
                predictions = model.predict(test_input, verbose=0)
                prediction_time = (time.time() - start_time) * 1000
                
                logger.info(f"  预测时间: {prediction_time:.2f}ms")
                logger.info(f"  预测形状: {predictions.shape}")
                
                # 计算置信度统计
                max_confidences = np.max(predictions, axis=1)
                avg_confidence = np.mean(max_confidences)
                confidence_std = np.std(max_confidences)
                
                logger.info(f"  平均置信度: {avg_confidence:.3f} ± {confidence_std:.3f}")
                
                # 类别分布
                predicted_classes = np.argmax(predictions, axis=1)
                class_distribution = {}
                for i in range(num_classes):
                    count = np.sum(predicted_classes == i)
                    class_distribution[class_names[i]] = count
                
                logger.info(f"  预测类别分布: {class_distribution}")
                
                results[model_name] = {
                    "type": "classification",
                    "input_shape": input_shape,
                    "output_shape": output_shape,
                    "parameters": param_count,
                    "prediction_time_ms": prediction_time,
                    "average_confidence": avg_confidence,
                    "confidence_std": confidence_std,
                    "class_distribution": class_distribution
                }
        
        except Exception as e:
            logger.error(f"{model_name} 评估失败: {e}")
    
    # 输出评估摘要
    logger.info("=== 模型性能评估摘要 ===")
    
    for model_name, result in results.items():
        logger.info(f"\n{model_name}:")
        logger.info(f"  类型: {result['type']}")
        logger.info(f"  参数量: {result['parameters']:,}")
        logger.info(f"  预测延迟: {result['prediction_time_ms']:.2f}ms")
        
        if result['type'] == 'trajectory_prediction':
            logger.info(f"  时间一致性: {result['temporal_consistency']:.3f}")
        else:
            logger.info(f"  平均置信度: {result['average_confidence']:.3f}")
    
    # 性能对比
    lstm_models = {k: v for k, v in results.items() if v['type'] == 'trajectory_prediction'}
    classifier_models = {k: v for k, v in results.items() if v['type'] == 'classification'}
    
    if len(lstm_models) > 1:
        logger.info("\nLSTM模型对比:")
        for name, result in lstm_models.items():
            logger.info(f"  {name}: {result['parameters']:,}参数, "
                       f"{result['prediction_time_ms']:.1f}ms延迟, "
                       f"{result['temporal_consistency']:.3f}一致性")
    
    if len(classifier_models) > 1:
        logger.info("\n分类器模型对比:")
        for name, result in classifier_models.items():
            logger.info(f"  {name}: {result['parameters']:,}参数, "
                       f"{result['prediction_time_ms']:.1f}ms延迟, "
                       f"{result['average_confidence']:.3f}置信度")
    
    logger.info("\n性能评估完成！")


def main():
    """主评估函数"""
    evaluate_models()


if __name__ == "__main__":
    main()
