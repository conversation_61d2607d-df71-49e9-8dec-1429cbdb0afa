"""
雷达数据处理模块
定义雷达站点和信号数据结构
"""

import numpy as np
from dataclasses import dataclass
from typing import List, Optional, Tuple
from datetime import datetime


@dataclass
class RadarStation:
    """雷达站点类"""
    station_id: str
    name: str
    position: Tuple[float, float, float]  # (x, y, z) 坐标
    detection_range: float  # 探测范围（km）
    frequency: float  # 工作频率（MHz）
    is_active: bool = True
    
    def distance_to(self, target_position: Tuple[float, float, float]) -> float:
        """计算到目标的距离"""
        dx = self.position[0] - target_position[0]
        dy = self.position[1] - target_position[1]
        dz = self.position[2] - target_position[2]
        return np.sqrt(dx**2 + dy**2 + dz**2)
    
    def can_detect(self, target_position: Tuple[float, float, float]) -> bool:
        """判断是否能探测到目标"""
        return self.distance_to(target_position) <= self.detection_range


@dataclass
class RadarSignal:
    """雷达信号数据类（增强版）"""
    timestamp: datetime
    station_id: str
    target_id: str

    # 基本测量数据
    toa: float  # 到达时间 (Time of Arrival)
    pa: float   # 相位角 (Phase Angle)

    # 目标状态信息
    position: Tuple[float, float, float]  # 位置 (x, y, z)
    position_accuracy: Tuple[float, float, float]  # 位置精度 (σx, σy, σz)
    velocity: Tuple[float, float, float]  # 速度 (vx, vy, vz)
    acceleration: Tuple[float, float, float]  # 加速度 (ax, ay, az)

    # 姿态信息（完整）
    heading: float      # 航向角
    elevation: float    # 高度角
    pitch: float        # 俯仰角
    roll: float         # 滚转角
    yaw: float          # 偏航角

    # 雷达测量参数
    target_distance: float    # 目标距离
    pulse_width: float        # 脉冲宽度
    radar_frequency: float    # 雷达工作频率

    # 信号特征
    prf: float                # 重复频率 (Pulse Repetition Frequency)
    signal_strength: float    # 信号强度
    communication_status: int # 通信状态标识 (0-正常, 1-干扰, 2-中断)

    # 信号质量
    snr: float          # 信噪比
    confidence: float   # 置信度
    
    def to_feature_vector(self) -> np.ndarray:
        """转换为增强特征向量（22维）"""
        return np.array([
            # 位置信息 (6维)
            self.position[0], self.position[1], self.position[2],
            self.position_accuracy[0], self.position_accuracy[1], self.position_accuracy[2],

            # 运动参数 (6维)
            self.velocity[0], self.velocity[1], self.velocity[2],
            self.acceleration[0], self.acceleration[1], self.acceleration[2],

            # 姿态信息 (5维)
            self.heading, self.elevation, self.pitch, self.roll, self.yaw,

            # 雷达测量参数 (3维)
            self.target_distance, self.pulse_width, self.radar_frequency,

            # 信号特征 (3维)
            self.prf, self.signal_strength, float(self.communication_status),

            # 基本测量和质量 (4维)
            self.toa, self.pa, self.snr, self.confidence
        ])

    def get_position_features(self) -> np.ndarray:
        """获取位置相关特征"""
        return np.array([
            self.position[0], self.position[1], self.position[2],
            self.position_accuracy[0], self.position_accuracy[1], self.position_accuracy[2]
        ])

    def get_motion_features(self) -> np.ndarray:
        """获取运动相关特征"""
        return np.array([
            self.velocity[0], self.velocity[1], self.velocity[2],
            self.acceleration[0], self.acceleration[1], self.acceleration[2]
        ])

    def get_attitude_features(self) -> np.ndarray:
        """获取姿态相关特征"""
        return np.array([
            self.heading, self.elevation, self.pitch, self.roll, self.yaw
        ])

    def get_radar_features(self) -> np.ndarray:
        """获取雷达测量特征"""
        return np.array([
            self.target_distance, self.pulse_width, self.radar_frequency,
            self.prf, self.signal_strength, float(self.communication_status)
        ])


@dataclass
class TargetTrajectory:
    """目标航迹类"""
    target_id: str
    target_type: str  # 战斗机、轰炸机等
    signals: List[RadarSignal]
    tactical_mode: Optional[str] = None  # 战术模式
    
    def get_time_series(self, feature_names: List[str]) -> np.ndarray:
        """获取时间序列数据"""
        if not self.signals:
            return np.array([])
        
        # 按时间排序
        sorted_signals = sorted(self.signals, key=lambda x: x.timestamp)
        
        # 提取特征
        features = []
        for signal in sorted_signals:
            feature_vector = signal.to_feature_vector()
            features.append(feature_vector)
        
        return np.array(features)
    
    def get_positions(self) -> np.ndarray:
        """获取位置序列"""
        positions = [signal.position for signal in self.signals]
        return np.array(positions)
    
    def get_velocities(self) -> np.ndarray:
        """获取速度序列"""
        velocities = [signal.velocity for signal in self.signals]
        return np.array(velocities)
    
    def duration(self) -> float:
        """获取航迹持续时间（秒）"""
        if len(self.signals) < 2:
            return 0.0
        
        sorted_signals = sorted(self.signals, key=lambda x: x.timestamp)
        start_time = sorted_signals[0].timestamp
        end_time = sorted_signals[-1].timestamp
        
        return (end_time - start_time).total_seconds()


class RadarDataManager:
    """雷达数据管理器"""
    
    def __init__(self):
        self.stations: List[RadarStation] = []
        self.trajectories: List[TargetTrajectory] = []
    
    def add_station(self, station: RadarStation):
        """添加雷达站点"""
        self.stations.append(station)
    
    def add_signal(self, signal: RadarSignal):
        """添加雷达信号"""
        # 查找对应的航迹
        trajectory = self.find_trajectory(signal.target_id)
        if trajectory is None:
            # 创建新航迹
            trajectory = TargetTrajectory(
                target_id=signal.target_id,
                target_type="unknown",
                signals=[]
            )
            self.trajectories.append(trajectory)
        
        trajectory.signals.append(signal)
    
    def find_trajectory(self, target_id: str) -> Optional[TargetTrajectory]:
        """查找目标航迹"""
        for trajectory in self.trajectories:
            if trajectory.target_id == target_id:
                return trajectory
        return None
    
    def get_active_stations(self) -> List[RadarStation]:
        """获取活跃的雷达站点"""
        return [station for station in self.stations if station.is_active]
