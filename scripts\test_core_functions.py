"""
核心功能测试脚本
测试项目的核心功能是否正常工作
"""

import sys
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置日志
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_data_structures():
    """测试数据结构"""
    logger.info("测试数据结构...")
    
    from src.data.radar_data import RadarSignal, TargetTrajectory, RadarStation
    
    # 测试雷达站点
    station = RadarStation(
        station_id="test_001",
        name="测试站点",
        position=(0, 0, 100),
        detection_range=200.0,
        frequency=3000.0
    )
    
    assert station.distance_to((100, 0, 100)) == 100.0
    assert station.can_detect((150, 0, 100)) == True
    assert station.can_detect((300, 0, 100)) == False
    
    # 测试雷达信号
    signal = RadarSignal(
        timestamp=datetime.now(),
        station_id="test_001",
        target_id="target_001",
        toa=0.1,
        pa=1.57,
        position=(100, 200, 1000),
        velocity=(150, 50, 0),
        heading=0.5,
        elevation=0.1,
        pitch=0.05,
        roll=0.02,
        snr=20.0,
        confidence=0.9
    )
    
    feature_vector = signal.to_feature_vector()
    assert len(feature_vector) == 14
    
    logger.info("✅ 数据结构测试通过")


def test_lstm_model():
    """测试LSTM模型"""
    logger.info("测试LSTM模型...")
    
    import tensorflow as tf
    
    # 检查模型文件是否存在
    model_path = "data/models/lstm_trajectory_model.h5"
    if not Path(model_path).exists():
        logger.error("LSTM模型文件不存在，请先运行训练脚本")
        return False
    
    # 加载模型
    model = tf.keras.models.load_model(model_path)
    
    # 测试预测
    test_input = np.random.randn(1, 20, 6)
    prediction = model.predict(test_input, verbose=0)
    
    assert prediction.shape == (1, 10, 3)
    
    logger.info("✅ LSTM模型测试通过")
    return True


def test_classifier():
    """测试分类器"""
    logger.info("测试战术分类器...")
    
    import tensorflow as tf
    
    # 检查模型文件是否存在
    model_path = "data/models/tactical_classifier_model.h5"
    if not Path(model_path).exists():
        logger.error("分类器模型文件不存在，请先运行训练脚本")
        return False
    
    # 加载模型
    model = tf.keras.models.load_model(model_path)
    
    # 测试预测
    test_input = np.random.randn(1, 10)
    prediction = model.predict(test_input, verbose=0)
    
    assert prediction.shape == (1, 6)  # 6个战术模式
    assert np.isclose(np.sum(prediction[0]), 1.0, atol=1e-6)  # 概率和为1
    
    logger.info("✅ 分类器测试通过")
    return True


def test_data_loader():
    """测试数据加载器"""
    logger.info("测试数据加载器...")
    
    from src.data.data_loader import RadarDataLoader
    
    loader = RadarDataLoader()
    
    # 测试生成示例数据
    trajectories = loader.generate_sample_data(num_targets=3, sequence_length=25)
    
    assert len(trajectories) == 3
    for trajectory in trajectories:
        assert len(trajectory.signals) == 25
        assert trajectory.target_id is not None
        assert trajectory.tactical_mode is not None
    
    logger.info("✅ 数据加载器测试通过")


def test_preprocessor():
    """测试数据预处理器"""
    logger.info("测试数据预处理器...")
    
    from src.data.preprocessor import DataPreprocessor
    from src.data.data_loader import RadarDataLoader
    
    # 生成测试数据
    loader = RadarDataLoader()
    trajectories = loader.generate_sample_data(num_targets=2, sequence_length=30)
    
    # 创建预处理器
    preprocessor = DataPreprocessor()
    
    # 测试特征提取
    features = preprocessor.extract_features(trajectories[0])
    assert features.shape[1] == 6  # 6维特征
    
    # 测试训练数据准备
    X, y = preprocessor.prepare_training_data(trajectories, sequence_length=20, prediction_horizon=10)
    
    assert X.shape[1] == 20  # 序列长度
    assert X.shape[2] == 6   # 特征维度
    assert y.shape[1] == 10  # 预测时间步
    assert y.shape[2] == 3   # 位置维度
    
    logger.info("✅ 数据预处理器测试通过")


def test_math_utils():
    """测试数学工具"""
    logger.info("测试数学工具...")
    
    from src.utils.math_utils import MathUtils
    
    # 测试距离计算
    pos1 = (0, 0, 0)
    pos2 = (3, 4, 0)
    distance = MathUtils.calculate_distance_3d(pos1, pos2)
    assert abs(distance - 5.0) < 1e-6
    
    # 测试速度计算
    velocity = MathUtils.calculate_velocity(pos1, pos2, 1.0)
    assert velocity == (3.0, 4.0, 0.0)
    
    # 测试航向角计算
    heading = MathUtils.calculate_heading((1, 1, 0))
    assert abs(heading - np.pi/4) < 1e-6
    
    logger.info("✅ 数学工具测试通过")


def main():
    """主测试函数"""
    logger.info("开始核心功能测试...")
    
    test_results = []
    
    # 运行各项测试
    try:
        test_data_structures()
        test_results.append("数据结构: ✅")
    except Exception as e:
        logger.error(f"数据结构测试失败: {e}")
        test_results.append("数据结构: ❌")
    
    try:
        lstm_ok = test_lstm_model()
        test_results.append("LSTM模型: ✅" if lstm_ok else "LSTM模型: ❌")
    except Exception as e:
        logger.error(f"LSTM模型测试失败: {e}")
        test_results.append("LSTM模型: ❌")
    
    try:
        classifier_ok = test_classifier()
        test_results.append("分类器: ✅" if classifier_ok else "分类器: ❌")
    except Exception as e:
        logger.error(f"分类器测试失败: {e}")
        test_results.append("分类器: ❌")
    
    try:
        test_data_loader()
        test_results.append("数据加载器: ✅")
    except Exception as e:
        logger.error(f"数据加载器测试失败: {e}")
        test_results.append("数据加载器: ❌")
    
    try:
        test_preprocessor()
        test_results.append("数据预处理器: ✅")
    except Exception as e:
        logger.error(f"数据预处理器测试失败: {e}")
        test_results.append("数据预处理器: ❌")
    
    try:
        test_math_utils()
        test_results.append("数学工具: ✅")
    except Exception as e:
        logger.error(f"数学工具测试失败: {e}")
        test_results.append("数学工具: ❌")
    
    # 输出测试结果
    logger.info("=== 核心功能测试结果 ===")
    for result in test_results:
        logger.info(f"  {result}")
    
    # 统计通过率
    passed = sum(1 for r in test_results if "✅" in r)
    total = len(test_results)
    logger.info(f"测试通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        logger.info("🎉 所有核心功能测试通过！")
    else:
        logger.warning("⚠️ 部分功能测试失败，请检查相关模块")


if __name__ == "__main__":
    main()
