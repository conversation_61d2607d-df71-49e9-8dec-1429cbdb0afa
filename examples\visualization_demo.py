"""
可视化演示
展示航迹预测的各种可视化功能
"""

import sys
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from typing import Dict, List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils.visualization import TrajectoryVisualizer
from src.data.radar_data import RadarSignal, TargetTrajectory
import tensorflow as tf

# 设置日志
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_sample_trajectories() -> List[TargetTrajectory]:
    """创建多个示例航迹"""
    trajectories = []
    tactical_modes = ["巡逻", "攻击突击", "空中格斗"]
    
    for target_idx in range(3):
        signals = []
        base_time = datetime.now()
        tactical_mode = tactical_modes[target_idx]
        
        # 根据战术模式生成不同的轨迹
        for t in range(30):
            if tactical_mode == "巡逻":
                # 巡逻：圆形轨迹
                angle = t * 0.2
                x = 2000 + 1000 * np.cos(angle)
                y = 3000 + 1000 * np.sin(angle)
                z = 10000 + np.random.normal(0, 50)
                vx, vy, vz = -200 * np.sin(angle), 200 * np.cos(angle), 0
            elif tactical_mode == "攻击突击":
                # 攻击：直线高速接近
                x = 8000 - t * 100
                y = 5000 + t * 50
                z = 8000 - t * 20
                vx, vy, vz = -400, 200, -80
            else:  # 空中格斗
                # 格斗：复杂机动
                x = 3000 + 500 * np.sin(t * 0.3) + t * 20
                y = 4000 + 300 * np.cos(t * 0.4) + t * 10
                z = 7000 + 200 * np.sin(t * 0.5)
                vx = 100 + 200 * np.cos(t * 0.3)
                vy = 50 + 150 * np.sin(t * 0.4)
                vz = 100 * np.cos(t * 0.5)
            
            # 添加噪声
            noise = np.random.normal(0, 20, 3)
            
            signal = RadarSignal(
                timestamp=base_time + timedelta(seconds=t),
                station_id="radar_001",
                target_id=f"target_{target_idx:03d}",
                toa=0.1 + t * 0.01,
                pa=np.pi/4 + t * 0.02,
                position=(x + noise[0], y + noise[1], z + noise[2]),
                velocity=(vx, vy, vz),
                heading=np.random.uniform(0, 2*np.pi),
                elevation=np.random.uniform(-np.pi/6, np.pi/6),
                pitch=np.random.uniform(-0.2, 0.2),
                roll=np.random.uniform(-0.1, 0.1),
                snr=np.random.uniform(15, 25),
                confidence=np.random.uniform(0.8, 0.95)
            )
            signals.append(signal)
        
        trajectory = TargetTrajectory(
            target_id=f"target_{target_idx:03d}",
            target_type="战斗机",
            signals=signals,
            tactical_mode=tactical_mode
        )
        trajectories.append(trajectory)
    
    return trajectories


def generate_predictions(trajectories: List[TargetTrajectory]) -> Dict[str, np.ndarray]:
    """为航迹生成示例预测"""
    predictions = {}
    
    # 加载LSTM模型
    model_path = "data/models/lstm_trajectory_model.h5"
    if not Path(model_path).exists():
        logger.warning("LSTM模型不存在，生成随机预测")
        # 生成随机预测
        for trajectory in trajectories:
            if len(trajectory.signals) > 0:
                last_pos = np.array(trajectory.signals[-1].position)
                last_vel = np.array(trajectory.signals[-1].velocity)
                
                # 简单的线性预测
                pred_positions = []
                for t in range(1, 11):  # 预测10个时间步
                    pred_pos = last_pos + last_vel * t * 1.0
                    pred_positions.append(pred_pos)
                
                predictions[trajectory.target_id] = np.array(pred_positions)
        
        return predictions
    
    try:
        # 使用训练好的模型进行预测
        model = tf.keras.models.load_model(model_path)
        
        for trajectory in trajectories:
            positions = trajectory.get_positions()
            if len(positions) >= 20:
                # 准备输入数据
                recent_positions = positions[-20:]
                velocities = trajectory.get_velocities()[-20:]
                
                lstm_input = np.zeros((1, 20, 6))
                for i in range(20):
                    lstm_input[0, i, :3] = recent_positions[i]
                    lstm_input[0, i, 3:6] = velocities[i]
                
                # 进行预测
                prediction = model.predict(lstm_input, verbose=0)
                predictions[trajectory.target_id] = prediction[0]
        
    except Exception as e:
        logger.error(f"模型预测失败: {e}")
    
    return predictions


def main():
    """主演示函数"""
    logger.info("开始可视化演示...")
    
    # 创建可视化器
    visualizer = TrajectoryVisualizer()
    
    # 创建示例数据
    trajectories = create_sample_trajectories()
    logger.info(f"创建了 {len(trajectories)} 个示例航迹")
    
    # 生成预测
    predictions = generate_predictions(trajectories)
    logger.info(f"生成了 {len(predictions)} 个预测结果")
    
    # 1. 绘制单个航迹的3D图
    logger.info("绘制单个航迹3D图...")
    for i, trajectory in enumerate(trajectories):
        prediction = predictions.get(trajectory.target_id)
        fig = visualizer.plot_trajectory_3d(
            trajectory, 
            prediction,
            save_path=f"trajectory_3d_{trajectory.target_id}.png"
        )
        plt.close(fig)  # 关闭图形以节省内存
    
    # 2. 绘制多目标航迹图
    logger.info("绘制多目标航迹图...")
    fig = visualizer.plot_multiple_trajectories(
        trajectories,
        predictions,
        save_path="multiple_trajectories.png"
    )
    plt.close(fig)
    
    # 3. 绘制战术模式分布
    logger.info("绘制战术模式分布...")
    fig = visualizer.plot_tactical_distribution(
        trajectories,
        save_path="tactical_distribution.png"
    )
    plt.close(fig)
    
    # 4. 如果有预测数据，绘制精度分析
    if predictions:
        logger.info("绘制预测精度分析...")
        # 使用第一个有预测的航迹
        for trajectory in trajectories:
            if trajectory.target_id in predictions:
                actual_pos = trajectory.get_positions()[-10:]  # 最后10个实际位置
                pred_pos = predictions[trajectory.target_id][:10]  # 前10个预测位置
                
                if len(actual_pos) == len(pred_pos):
                    fig = visualizer.plot_prediction_accuracy(
                        actual_pos,
                        pred_pos,
                        save_path="prediction_accuracy.png"
                    )
                    plt.close(fig)
                    break
    
    # 5. 创建交互式图表（保存为HTML）
    logger.info("创建交互式图表...")
    try:
        for trajectory in trajectories:
            prediction = predictions.get(trajectory.target_id)
            fig = visualizer.plot_interactive_3d(trajectory, prediction)
            
            html_path = f"interactive_trajectory_{trajectory.target_id}.html"
            fig.write_html(html_path)
            logger.info(f"交互式图表已保存: {html_path}")
    except Exception as e:
        logger.warning(f"交互式图表创建失败: {e}")
    
    logger.info("可视化演示完成！")
    logger.info("生成的文件:")
    logger.info("  - trajectory_3d_*.png: 单个航迹3D图")
    logger.info("  - multiple_trajectories.png: 多目标航迹图")
    logger.info("  - tactical_distribution.png: 战术模式分布图")
    logger.info("  - prediction_accuracy.png: 预测精度分析图")
    logger.info("  - interactive_trajectory_*.html: 交互式图表")


if __name__ == "__main__":
    main()
