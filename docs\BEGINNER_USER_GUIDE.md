# 🚀 AirModel_V0 零基础用户完整使用指南

## 📖 文档概述

欢迎使用AirModel_V0增强目标航迹ZZ跟踪实时预测模型！本指南专为零基础用户设计，将手把手教您从环境安装到实际应用的完整流程。无论您是否有编程经验，都能通过本指南成功运行和使用系统。

### 🎯 您将学到什么
- ✅ 如何安装和配置运行环境
- ✅ 理解27维特征向量的含义和作用
- ✅ 如何准备和组织雷达数据
- ✅ 完整的模型训练流程
- ✅ 如何使用训练好的模型进行预测
- ✅ API接口的使用方法
- ✅ 实际应用场景演示

---

## 🌟 1. 项目概述与环境准备

### 1.1 项目功能简介

**AirModel_V0** 是一个世界领先的军用航迹预测与战术识别系统，主要功能包括：

#### 🎯 核心功能
- **航迹预测**: 根据历史数据预测飞行器未来的飞行轨迹
- **战术识别**: 识别飞行器的战术意图（巡逻、攻击、侦察等8种模式）
- **多站点协同**: 融合多个雷达站点的观测数据
- **实时处理**: 毫秒级响应的实时预测能力

#### 🏆 应用场景
- **军事应用**: 空中威胁评估、战术意图识别、防空作战
- **民用航空**: 航班轨迹预测、空中交通管制
- **科研应用**: 时序预测研究、多源数据融合

### 1.2 系统要求

在开始之前，请确保您的计算机满足以下要求：

#### 💻 硬件要求
- **CPU**: Intel i5或AMD同等级别以上（推荐i7/i9）
- **内存**: 最少8GB RAM（推荐16GB或更多）
- **存储**: 至少5GB可用空间
- **GPU**: NVIDIA显卡（可选，用于加速训练）
  - 支持CUDA 11.2或更高版本
  - 最少4GB显存（推荐8GB或更多）

#### 🖥️ 操作系统
- **Windows**: Windows 10/11（推荐）
- **Linux**: Ubuntu 18.04+, CentOS 7+
- **macOS**: macOS 10.15+

### 1.3 Python环境安装

#### 步骤1: 安装Python

**Windows用户**:
1. 访问 [Python官网](https://www.python.org/downloads/)
2. 下载Python 3.8或更高版本（推荐3.11）
3. 运行安装程序，**重要**: 勾选"Add Python to PATH"
4. 验证安装：打开命令提示符，输入 `python --version`

**Linux用户**:
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3.11 python3.11-pip python3.11-venv

# CentOS/RHEL
sudo yum install python3.11 python3.11-pip
```

**macOS用户**:
```bash
# 使用Homebrew（推荐）
brew install python@3.11

# 或者从官网下载安装包
```

#### 步骤2: 验证Python安装

打开终端/命令提示符，输入以下命令：
```bash
python --version
pip --version
```

**期望输出**:
```
Python 3.11.x
pip 23.x.x from ...
```

### 1.4 项目下载与环境配置

#### 步骤1: 下载项目

**方法1: 使用Git（推荐）**
```bash
# 安装Git（如果尚未安装）
# Windows: 从 https://git-scm.com/ 下载安装
# Linux: sudo apt install git
# macOS: brew install git

# 克隆项目
git clone <repository-url>
cd AirModel_V0
```

**方法2: 直接下载**
1. 从项目页面下载ZIP文件
2. 解压到您选择的目录
3. 打开终端，进入项目目录

#### 步骤2: 创建虚拟环境

虚拟环境可以避免不同项目之间的依赖冲突：

```bash
# 创建虚拟环境
python -m venv airmodel_env

# 激活虚拟环境
# Windows:
airmodel_env\Scripts\activate

# Linux/macOS:
source airmodel_env/bin/activate
```

**成功激活后，命令提示符前会显示 `(airmodel_env)`**

#### 步骤3: 安装项目依赖

```bash
# 确保在项目根目录下
pip install --upgrade pip

# 安装所有依赖包
pip install -r requirements.txt
```

**安装过程可能需要5-15分钟，请耐心等待**

#### 步骤4: 配置环境变量

```bash
# 复制环境配置文件
cp .env.example .env

# Windows用户使用:
copy .env.example .env
```

使用文本编辑器打开 `.env` 文件，根据需要修改配置：

```bash
# 基本配置（通常不需要修改）
MODEL_PATH=data/models
API_HOST=0.0.0.0
API_PORT=8000
LOG_LEVEL=INFO

# GPU配置（如果有NVIDIA显卡）
USE_GPU=true
GPU_MEMORY_LIMIT=4096
```

### 1.5 验证安装

运行以下命令验证环境配置是否正确：

```bash
# 测试核心功能
python scripts/test_core_functions.py
```

**期望输出**:
```
INFO - 测试数据结构...
INFO - 测试数据生成器...
INFO - 测试预处理器...
INFO - 所有核心功能测试通过！
```

如果看到错误信息，请检查：
1. Python版本是否正确
2. 虚拟环境是否激活
3. 依赖包是否完全安装

### 1.6 目录结构说明

了解项目结构有助于更好地使用系统：

```
AirModel_V0/
├── 📂 src/                    # 🔥 核心源代码
│   ├── 📂 api/               # REST API和WebSocket接口
│   ├── 📂 data/              # 数据处理模块
│   ├── 📂 models/            # 深度学习模型
│   ├── 📂 prediction/        # 预测引擎
│   └── 📂 utils/             # 工具函数
├── 📂 examples/              # 🎮 演示程序（重要！）
│   ├── quick_demo.py         # 快速入门演示
│   ├── enhanced_demo.py      # 增强功能演示
│   └── multi_station_demo.py # 多站点演示
├── 📂 scripts/               # 🛠️ 训练和测试脚本
│   ├── simple_train.py       # 简化训练流程
│   ├── train_classifier.py   # 分类器训练
│   └── test_core_functions.py # 功能测试
├── 📂 config/                # ⚙️ 配置文件
├── 📂 data/                  # 📊 数据目录
│   ├── 📂 models/            # 训练好的模型（自动创建）
│   ├── 📂 raw/               # 原始数据
│   └── 📂 processed/         # 处理后数据
├── 📂 docs/                  # 📚 详细文档
└── 📂 logs/                  # 📝 日志文件（自动创建）
```

#### 🎯 重要目录说明

- **examples/**: 包含各种演示程序，是学习的最佳起点
- **scripts/**: 包含训练脚本，用于训练自己的模型
- **data/models/**: 存放训练好的模型文件
- **logs/**: 系统运行日志，出现问题时查看

---

## 📊 2. 数据组织与准备

### 2.1 27维特征向量详细解释

AirModel_V0使用27维特征向量来描述飞行器的完整状态。理解这些特征对于使用系统至关重要。

#### 🎯 特征分组概览

| 特征组 | 维度数 | 主要作用 | 重要性 |
|--------|--------|----------|--------|
| **位置信息** | 6维 | 描述飞行器的空间位置和精度 | ⭐⭐⭐⭐⭐ |
| **运动参数** | 6维 | 描述飞行器的运动状态 | ⭐⭐⭐⭐⭐ |
| **姿态信息** | 5维 | 描述飞行器的飞行姿态 | ⭐⭐⭐⭐ |
| **雷达测量** | 3维 | 雷达系统的测量参数 | ⭐⭐⭐ |
| **信号特征** | 3维 | 信号质量和通信状态 | ⭐⭐⭐ |
| **基本测量** | 4维 | TOA/PA和信号质量 | ⭐⭐⭐⭐ |

#### 📍 位置信息（6维）- 最重要

**维度1-3: 空间坐标 (x, y, z)**
- **含义**: 飞行器在三维空间中的位置
- **单位**: 米（m）
- **取值范围**:
  - x, y: -50,000 到 50,000 米
  - z: 0 到 30,000 米（高度）
- **实际意义**:
  - x: 东西方向位置（正值为东）
  - y: 南北方向位置（正值为北）
  - z: 海拔高度

**示例**:
```python
position = (5000.0, 3000.0, 8000.0)
# 表示：东5公里，北3公里，高度8公里
```

**维度4-6: 位置精度 (σx, σy, σz)**
- **含义**: 位置测量的不确定性
- **单位**: 米（m）
- **取值范围**: 1.0 到 100.0 米
- **实际意义**: 数值越小，位置越精确

**示例**:
```python
position_accuracy = (10.0, 12.0, 20.0)
# 表示：x方向精度±10米，y方向±12米，z方向±20米
```

#### 🏃 运动参数（6维）- 最重要

**维度7-9: 速度分量 (vx, vy, vz)**
- **含义**: 飞行器在三个方向上的速度
- **单位**: 米/秒（m/s）
- **取值范围**: -1000 到 1000 m/s
- **实际意义**:
  - vx: 东西方向速度
  - vy: 南北方向速度
  - vz: 垂直方向速度（正值为上升）

**示例**:
```python
velocity = (250.0, 150.0, 30.0)
# 表示：向东250m/s，向北150m/s，上升30m/s
# 总速度 = √(250² + 150² + 30²) ≈ 292 m/s ≈ 1051 km/h
```

**维度10-12: 加速度分量 (ax, ay, az)**
- **含义**: 飞行器的加速度变化
- **单位**: 米/秒²（m/s²）
- **取值范围**: -50 到 50 m/s²
- **实际意义**: 反映飞行器的机动性能

**示例**:
```python
acceleration = (5.0, -2.0, 1.0)
# 表示：向东加速5m/s²，向南减速2m/s²，向上加速1m/s²
```

#### ✈️ 姿态信息（5维）- 重要

**维度13: 航向角 (heading)**
- **含义**: 飞行器机头指向的方向
- **单位**: 弧度（rad）
- **取值范围**: 0 到 2π
- **实际意义**: 0为正北，π/2为正东，π为正南，3π/2为正西

**维度14: 高度角 (elevation)**
- **含义**: 飞行器相对于水平面的角度
- **单位**: 弧度（rad）
- **取值范围**: -π/2 到 π/2
- **实际意义**: 正值为上升，负值为下降

**维度15-17: 姿态角 (pitch, roll, yaw)**
- **pitch（俯仰角）**: 机头上下摆动
- **roll（滚转角）**: 机身左右翻滚
- **yaw（偏航角）**: 机头左右摆动

**示例**:
```python
# 角度转换：度 → 弧度
import math
heading_degrees = 45  # 东北方向
heading_radians = math.radians(heading_degrees)  # 0.785弧度
```

#### 📡 雷达测量参数（3维）

**维度18: 目标距离 (target_distance)**
- **含义**: 雷达到目标的直线距离
- **单位**: 米（m）
- **计算公式**: `√(x² + y² + z²)`

**维度19: 脉冲宽度 (pulse_width)**
- **含义**: 雷达脉冲的持续时间
- **单位**: 微秒（μs）
- **影响**: 影响距离分辨率

**维度20: 雷达频率 (radar_frequency)**
- **含义**: 雷达工作频率
- **单位**: 兆赫兹（MHz）
- **典型值**: 2800-3200 MHz

#### 📶 信号特征（3维）

**维度21: 脉冲重复频率 (PRF)**
- **含义**: 雷达脉冲的重复频率
- **单位**: 赫兹（Hz）
- **影响**: 影响速度测量精度

**维度22: 信号强度 (signal_strength)**
- **含义**: 接收信号的强度
- **取值范围**: 0.0 到 1.0
- **实际意义**: 1.0为最强，0.0为最弱

**维度23: 通信状态 (communication_status)**
- **含义**: 通信链路状态
- **取值**: 0（正常）、1（干扰）、2（中断）
- **重要性**: 影响数据可靠性

#### 📊 基本测量和质量（4维）

**维度24: TOA（到达时间）**
- **含义**: 信号从发射到接收的时间
- **单位**: 秒（s）
- **用途**: 多站点时间同步

**维度25: PA（相位角）**
- **含义**: 信号的相位信息
- **单位**: 弧度（rad）
- **用途**: 方向测量

**维度26: 信噪比 (SNR)**
- **含义**: 信号与噪声的比值
- **单位**: 分贝（dB）
- **典型值**: 15-30 dB

**维度27: 置信度 (confidence)**
- **含义**: 测量结果的可信程度
- **取值范围**: 0.0 到 1.0
- **重要性**: 用于数据融合权重

### 2.2 雷达信号数据的创建和格式化

#### 🎯 RadarSignal数据结构

系统使用 `RadarSignal` 类来表示单个雷达观测数据：

```python
from datetime import datetime
from src.data.radar_data import RadarSignal

# 创建一个雷达信号示例
signal = RadarSignal(
    # 基础信息
    timestamp=datetime.now(),           # 当前时间
    station_id="radar_001",            # 雷达站点ID
    target_id="target_001",            # 目标ID

    # TOA/PA数据
    toa=0.15,                          # 到达时间（秒）
    pa=0.785,                          # 相位角（弧度）

    # 位置信息
    position=(5000.0, 3000.0, 8000.0), # 位置坐标
    position_accuracy=(10.0, 12.0, 20.0), # 位置精度

    # 运动信息
    velocity=(250.0, 150.0, 30.0),     # 速度向量
    acceleration=(5.0, -2.0, 1.0),     # 加速度向量

    # 姿态信息
    heading=0.524,                     # 航向角（30度）
    elevation=0.118,                   # 高度角
    pitch=0.1, roll=-0.05, yaw=0.02,  # 姿态角

    # 雷达参数
    target_distance=9434.0,            # 目标距离
    pulse_width=1.2,                   # 脉冲宽度
    radar_frequency=3000.0,            # 雷达频率

    # 信号特征
    prf=2500.0,                        # 脉冲重复频率
    signal_strength=0.85,              # 信号强度
    communication_status=0,            # 通信状态（正常）

    # 信号质量
    snr=20.5,                          # 信噪比
    confidence=0.92                    # 置信度
)
```

#### 📝 实际数据创建示例

**示例1: 战斗机巡逻任务**
```python
import math
from datetime import datetime

def create_fighter_patrol_signal():
    """创建战斗机巡逻任务的雷达信号"""
    return RadarSignal(
        timestamp=datetime.now(),
        station_id="radar_001",
        target_id="fighter_001",

        # 巡逻特征：稳定速度，规律航线
        toa=0.12,
        pa=1.047,  # 60度
        position=(8000.0, 5000.0, 10000.0),  # 高空飞行
        velocity=(180.0, 120.0, 0.0),        # 稳定水平飞行
        acceleration=(2.0, -1.0, 0.5),       # 轻微机动

        heading=0.588,  # 约33.7度
        elevation=0.0,  # 水平飞行
        pitch=0.05, roll=0.02, yaw=0.01,    # 稳定姿态

        target_distance=12207.0,
        pulse_width=1.0,
        radar_frequency=3000.0,
        prf=2000.0,
        signal_strength=0.82,
        communication_status=0,  # 正常
        snr=22.5,
        confidence=0.91
    )
```

**示例2: 预警机探测任务**
```python
def create_awacs_detection_signal():
    """创建预警机探测任务的雷达信号"""
    return RadarSignal(
        timestamp=datetime.now(),
        station_id="radar_002",
        target_id="awacs_001",

        # 预警机特征：高空慢速，大范围扫描
        toa=0.18,
        pa=2.094,  # 120度
        position=(15000.0, 8000.0, 12000.0), # 更高空飞行
        velocity=(120.0, 80.0, 5.0),         # 较慢速度
        acceleration=(0.5, 0.2, 0.1),        # 轻微加速

        heading=0.785,  # 45度
        elevation=0.087, # 轻微上升
        pitch=0.02, roll=-0.01, yaw=0.005,  # 非常稳定

        target_distance=19209.0,
        pulse_width=2.5,  # 更长脉宽
        radar_frequency=2800.0,
        prf=1500.0,       # 较低PRF
        signal_strength=0.75,
        communication_status=0,
        snr=18.2,
        confidence=0.88
    )
```

#### 🔧 数据验证和检查

创建数据后，建议进行验证：

```python
def validate_radar_signal(signal):
    """验证雷达信号数据的有效性"""
    errors = []

    # 检查位置范围
    x, y, z = signal.position
    if not (-50000 <= x <= 50000):
        errors.append(f"X坐标超出范围: {x}")
    if not (-50000 <= y <= 50000):
        errors.append(f"Y坐标超出范围: {y}")
    if not (0 <= z <= 30000):
        errors.append(f"高度超出范围: {z}")

    # 检查速度范围
    vx, vy, vz = signal.velocity
    if not (-1000 <= vx <= 1000):
        errors.append(f"X方向速度超出范围: {vx}")

    # 检查置信度
    if not (0.0 <= signal.confidence <= 1.0):
        errors.append(f"置信度超出范围: {signal.confidence}")

    return errors

# 使用示例
signal = create_fighter_patrol_signal()
errors = validate_radar_signal(signal)
if errors:
    print("数据验证失败:")
    for error in errors:
        print(f"  - {error}")
else:
    print("数据验证通过！")
```

### 2.3 多站点数据的组织方式

#### 🌐 多站点协同观测概念

多站点协同观测是AirModel_V0的核心创新功能，通过多个雷达站点同时观测同一目标，提高测量精度和可靠性。

**基本原理**:
1. **多个雷达站点**同时观测同一目标
2. **时间同步**确保数据的时间一致性
3. **数据融合**将多站点数据合并为更准确的结果

#### 🏗️ 雷达站点配置

首先需要定义雷达站点：

```python
from src.data.radar_data import RadarStation

# 创建多个雷达站点
stations = [
    RadarStation(
        station_id="radar_001",
        name="东部站点",
        position=(0.0, 0.0, 100.0),      # 站点位置
        detection_range=200.0,           # 探测范围（km）
        frequency=3000.0,                # 工作频率（MHz）
        is_active=True
    ),
    RadarStation(
        station_id="radar_002",
        name="西部站点",
        position=(150.0, 50.0, 120.0),
        detection_range=180.0,
        frequency=3200.0,
        is_active=True
    ),
    RadarStation(
        station_id="radar_003",
        name="南部站点",
        position=(75.0, -100.0, 110.0),
        detection_range=190.0,
        frequency=2800.0,
        is_active=True
    )
]
```

#### ⏰ 时间同步要求

多站点数据的关键是时间同步：

**时间窗口**: 0.1-0.2秒内的信号被认为是同步的
**时间格式**: ISO 8601标准格式
**同步精度**: ±50毫秒

```python
from datetime import datetime, timedelta

# 创建同一目标的多站点观测数据
base_time = datetime.now()

# 站点1的观测（基准时间）
signal_1 = RadarSignal(
    timestamp=base_time,
    station_id="radar_001",
    target_id="target_001",
    toa=0.15, pa=0.785,
    position=(5000.0, 3000.0, 8000.0),
    # ... 其他参数
)

# 站点2的观测（50ms延迟）
signal_2 = RadarSignal(
    timestamp=base_time + timedelta(milliseconds=50),
    station_id="radar_002",
    target_id="target_001",  # 同一目标
    toa=0.12, pa=0.820,     # 略有不同的TOA/PA
    position=(5020.0, 2980.0, 8010.0),  # 略有不同的位置
    # ... 其他参数
)

# 站点3的观测（80ms延迟）
signal_3 = RadarSignal(
    timestamp=base_time + timedelta(milliseconds=80),
    station_id="radar_003",
    target_id="target_001",  # 同一目标
    toa=0.18, pa=0.750,
    position=(4980.0, 3020.0, 7990.0),
    # ... 其他参数
)
```

#### 🔄 数据融合过程

```python
from src.data.multi_station_fusion import MultiStationDataFusion

# 创建多站点融合引擎
fusion_engine = MultiStationDataFusion(
    time_window=0.1,    # 100ms时间窗口
    min_stations=2      # 最少2个站点
)

# 添加雷达站点
for station in stations:
    fusion_engine.add_station(station)

# 添加观测信号
fusion_engine.add_signal(signal_1)
fusion_engine.add_signal(signal_2)
fusion_engine.add_signal(signal_3)

# 创建融合航迹
fused_trajectory = fusion_engine.create_fused_trajectory("target_001")

if fused_trajectory:
    print(f"融合成功！融合信号数: {len(fused_trajectory.signals)}")
    print(f"融合置信度: {fused_trajectory.signals[0].confidence:.3f}")
else:
    print("融合失败，请检查数据")
```

### 2.4 数据预处理流程

#### 🔧 归一化处理

数据归一化是机器学习的重要步骤：

```python
from src.data.preprocessor import DataPreprocessor

# 创建预处理器
preprocessor = DataPreprocessor()

# 示例：归一化位置数据
raw_positions = [
    (5000.0, 3000.0, 8000.0),
    (8000.0, 5000.0, 10000.0),
    (15000.0, 8000.0, 12000.0)
]

# MinMax归一化（默认方法）
normalized_positions = preprocessor.normalize_positions(raw_positions)
print("归一化后的位置:")
for i, pos in enumerate(normalized_positions):
    print(f"  原始: {raw_positions[i]} -> 归一化: {pos}")
```

**输出示例**:
```
归一化后的位置:
  原始: (5000.0, 3000.0, 8000.0) -> 归一化: (0.2, 0.15, 0.267)
  原始: (8000.0, 5000.0, 10000.0) -> 归一化: (0.32, 0.25, 0.333)
  原始: (15000.0, 8000.0, 12000.0) -> 归一化: (0.6, 0.4, 0.4)
```

#### 🚨 异常值处理

```python
import numpy as np

def detect_and_handle_outliers(data, method="3sigma"):
    """检测和处理异常值"""
    if method == "3sigma":
        mean = np.mean(data)
        std = np.std(data)
        threshold = 3 * std

        # 检测异常值
        outliers = np.abs(data - mean) > threshold

        # 处理异常值（截断到边界）
        data_cleaned = data.copy()
        data_cleaned[outliers] = np.clip(
            data_cleaned[outliers],
            mean - threshold,
            mean + threshold
        )

        return data_cleaned, outliers

# 使用示例
velocities = np.array([200, 250, 180, 1500, 220, 190])  # 1500是异常值
cleaned_velocities, outlier_mask = detect_and_handle_outliers(velocities)

print("原始速度:", velocities)
print("清理后速度:", cleaned_velocities)
print("异常值位置:", np.where(outlier_mask)[0])
```

#### ⏱️ 时序对齐算法

```python
def align_time_series_data(signals, time_window=0.1):
    """对齐时序数据"""
    # 按时间排序
    sorted_signals = sorted(signals, key=lambda x: x.timestamp)

    aligned_groups = []
    current_group = [sorted_signals[0]]
    base_time = sorted_signals[0].timestamp

    for signal in sorted_signals[1:]:
        time_diff = (signal.timestamp - base_time).total_seconds()

        if time_diff <= time_window:
            # 在时间窗口内，加入当前组
            current_group.append(signal)
        else:
            # 超出时间窗口，开始新组
            if len(current_group) >= 2:  # 至少2个信号
                aligned_groups.append(current_group)

            current_group = [signal]
            base_time = signal.timestamp

    # 添加最后一组
    if len(current_group) >= 2:
        aligned_groups.append(current_group)

    return aligned_groups

# 使用示例
signals = [signal_1, signal_2, signal_3]  # 之前创建的信号
aligned_groups = align_time_series_data(signals)

print(f"对齐后的组数: {len(aligned_groups)}")
for i, group in enumerate(aligned_groups):
    print(f"  组{i+1}: {len(group)}个信号")

---

## 🎓 3. 模型训练完整流程

### 3.1 训练前的准备工作

#### 📋 检查环境和数据

在开始训练之前，请确保：

```bash
# 1. 检查虚拟环境是否激活
# 命令提示符应该显示 (airmodel_env)

# 2. 检查GPU是否可用（可选）
python -c "import tensorflow as tf; print('GPU可用:', tf.config.list_physical_devices('GPU'))"

# 3. 检查数据目录
ls -la data/  # Linux/macOS
dir data\     # Windows
```

#### 🎯 理解训练目标

AirModel_V0包含两个主要模型：

1. **LSTM航迹预测模型**: 预测飞行器未来的轨迹
2. **战术分类器**: 识别飞行器的战术意图

### 3.2 LSTM模型训练详细步骤

#### 步骤1: 数据准备

```bash
# 运行简化的LSTM训练脚本
python scripts/simple_train.py
```

**训练过程输出示例**:
```
INFO - 开始简化训练流程...
INFO - 生成示例训练数据...
INFO - 训练数据: (700, 20, 6), 验证数据: (300, 20, 6)
INFO - 构建LSTM模型...
INFO - 模型构建完成，参数量: 210910
INFO - 开始训练模型...

Epoch 1/20
22/22 [==============================] - 8s 200ms/step - loss: 0.8234 - mae: 0.7123 - val_loss: 0.7456 - val_mae: 0.6789
Epoch 2/20
22/22 [==============================] - 2s 95ms/step - loss: 0.6789 - mae: 0.6234 - val_loss: 0.6123 - val_mae: 0.5678
...
Epoch 15/20
22/22 [==============================] - 2s 95ms/step - loss: 0.1234 - mae: 0.2345 - val_loss: 0.1456 - val_mae: 0.2567

INFO - 模型训练完成并保存到: data/models/lstm_trajectory_model.h5
INFO - 验证集性能 - Loss: 0.1456, MAE: 0.2567
```

#### 步骤2: 理解训练参数

打开 `scripts/simple_train.py` 查看关键参数：

```python
def create_lstm_model(sequence_length=20, input_dim=6, prediction_horizon=10, output_dim=3):
    """
    创建LSTM模型

    参数说明:
    - sequence_length: 输入序列长度（使用过去20个时间点）
    - input_dim: 输入特征维度（位置+速度，共6维）
    - prediction_horizon: 预测时间步长（预测未来10个时间点）
    - output_dim: 输出维度（预测位置，3维）
    """
```

#### 步骤3: 模型配置详解

```python
# LSTM网络结构
x = layers.LSTM(128, return_sequences=True, dropout=0.2, name="lstm_1")(inputs)
x = layers.LSTM(128, return_sequences=False, dropout=0.2, name="lstm_2")(x)

# 参数说明:
# - 128: LSTM隐藏单元数量
# - return_sequences=True: 第一层返回完整序列
# - dropout=0.2: 20%的神经元随机失活，防止过拟合
```

#### 步骤4: 训练过程监控

**关键指标解释**:
- **loss**: 训练损失，越小越好
- **mae**: 平均绝对误差，越小越好
- **val_loss**: 验证损失，用于判断过拟合
- **val_mae**: 验证平均绝对误差

**正常训练的特征**:
- 损失逐渐下降
- 训练损失和验证损失差距不大（避免过拟合）
- MAE在合理范围内（通常<1.0）

#### 步骤5: 模型保存和验证

训练完成后，模型会自动保存到 `data/models/lstm_trajectory_model.h5`

```python
# 验证模型是否正确保存
import tensorflow as tf

model = tf.keras.models.load_model('data/models/lstm_trajectory_model.h5')
print(f"模型输入形状: {model.input_shape}")
print(f"模型输出形状: {model.output_shape}")
print(f"模型参数量: {model.count_params()}")
```

### 3.3 战术分类器训练方法

#### 步骤1: 启动分类器训练

```bash
# 运行战术分类器训练脚本
python scripts/train_classifier.py
```

**训练过程输出示例**:
```
INFO - 开始战术分类器训练...
INFO - 生成战术特征数据...
INFO - 训练数据: (700, 10), 验证数据: (300, 10)
INFO - 战术模式类别: ['巡逻任务', '预警探测', '电子侦察', '电子干扰', '对空攻击', '对地攻击']
INFO - 构建战术模式分类器...
INFO - 分类器构建完成，参数量: 13476

Epoch 1/50
22/22 [==============================] - 2s 45ms/step - loss: 1.7234 - accuracy: 0.2345 - val_loss: 1.6789 - val_accuracy: 0.2567
Epoch 2/50
22/22 [==============================] - 1s 25ms/step - loss: 1.5678 - accuracy: 0.3456 - val_loss: 1.4567 - val_accuracy: 0.3789
...
Epoch 35/50
22/22 [==============================] - 1s 25ms/step - loss: 0.3456 - accuracy: 0.8765 - val_loss: 0.4123 - val_accuracy: 0.8234

INFO - 分类器训练完成并保存到: data/models/tactical_classifier_model.h5
INFO - 验证集性能 - Loss: 0.4123, Accuracy: 0.8234, Top-K Accuracy: 0.9456
```

#### 步骤2: 理解战术分类

系统支持6种基本战术模式：

1. **巡逻任务**: 稳定速度，规律航线
2. **预警探测**: 高空慢速，大范围扫描
3. **电子侦察**: 复杂航线，中速机动
4. **电子干扰**: 固定位置，低速悬停
5. **对空攻击**: 高速接近，直线攻击
6. **对地攻击**: 低空突防，俯冲攻击

#### 步骤3: 分类器性能指标

**关键指标解释**:
- **accuracy**: 分类准确率，>80%为良好
- **top_k_accuracy**: Top-K准确率，考虑前几个最可能的类别
- **loss**: 分类损失，越小越好

### 3.4 训练参数调优建议

#### 🎛️ LSTM模型调优

**如果训练损失下降缓慢**:
```python
# 增加学习率
optimizer=keras.optimizers.Adam(learning_rate=0.002)  # 从0.001增加到0.002

# 增加LSTM单元数
x = layers.LSTM(256, return_sequences=True, dropout=0.2)(inputs)  # 从128增加到256
```

**如果出现过拟合（验证损失上升）**:
```python
# 增加Dropout率
x = layers.LSTM(128, return_sequences=True, dropout=0.3)(inputs)  # 从0.2增加到0.3

# 减少模型复杂度
x = layers.LSTM(64, return_sequences=True, dropout=0.2)(inputs)   # 从128减少到64
```

**如果训练时间过长**:
```python
# 减少训练轮数
epochs=10  # 从20减少到10

# 增加批大小
batch_size=64  # 从32增加到64
```

#### 🎯 分类器调优

**如果分类准确率低于70%**:
```python
# 增加隐藏层大小
x = layers.Dense(128, activation="relu")(inputs)  # 从64增加到128

# 增加训练轮数
epochs=100  # 从50增加到100

# 降低学习率
optimizer=keras.optimizers.Adam(learning_rate=0.0005)  # 从0.001降低到0.0005
```

### 3.5 训练过程监控和结果评估

#### 📊 训练日志分析

训练过程中的日志保存在 `logs/` 目录下：

```bash
# 查看训练日志
tail -f logs/airmodel.log

# 搜索错误信息
grep "ERROR" logs/airmodel.log

# 搜索性能信息
grep "performance" logs/airmodel.log
```

#### 📈 可视化训练过程

```python
import matplotlib.pyplot as plt

def plot_training_history(history):
    """可视化训练历史"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))

    # 损失曲线
    ax1.plot(history.history['loss'], label='训练损失')
    ax1.plot(history.history['val_loss'], label='验证损失')
    ax1.set_title('模型损失')
    ax1.set_xlabel('训练轮次')
    ax1.set_ylabel('损失值')
    ax1.legend()

    # 准确率曲线（如果有）
    if 'accuracy' in history.history:
        ax2.plot(history.history['accuracy'], label='训练准确率')
        ax2.plot(history.history['val_accuracy'], label='验证准确率')
        ax2.set_title('模型准确率')
        ax2.set_xlabel('训练轮次')
        ax2.set_ylabel('准确率')
        ax2.legend()

    plt.tight_layout()
    plt.savefig('training_history.png')
    plt.show()

# 在训练脚本中使用
# history = model.fit(...)
# plot_training_history(history)
```

#### ✅ 模型质量评估

```python
def evaluate_model_quality():
    """评估模型质量"""
    import tensorflow as tf

    # 加载模型
    lstm_model = tf.keras.models.load_model('data/models/lstm_trajectory_model.h5')
    classifier_model = tf.keras.models.load_model('data/models/tactical_classifier_model.h5')

    print("=== 模型质量评估 ===")
    print(f"LSTM模型参数量: {lstm_model.count_params():,}")
    print(f"分类器参数量: {classifier_model.count_params():,}")

    # 检查模型结构
    print("\nLSTM模型结构:")
    lstm_model.summary()

    print("\n分类器模型结构:")
    classifier_model.summary()

    return lstm_model, classifier_model

# 运行评估
lstm_model, classifier_model = evaluate_model_quality()
```

#### 🎯 性能基准测试

```bash
# 运行综合性能评估
python examples/comprehensive_evaluation.py
```

**期望输出**:
```
=== 综合性能评估 ===
📊 LSTM航迹预测性能:
  - 平均预测误差: 0.234
  - 时间一致性: 0.992
  - 预测延迟: 1.6s

🎯 战术分类器性能:
  - 分类准确率: 0.846
  - 平均置信度: 0.823
  - 预测延迟: 335ms

🌐 多站点融合性能:
  - 融合置信度: 0.902
  - 数据对齐成功率: 0.95
  - 融合延迟: 50ms
```

---

## 🎮 4. 模型使用方法

### 4.1 如何加载训练好的模型

#### 📂 检查模型文件

首先确认模型文件是否存在：

```bash
# 检查模型目录
ls -la data/models/

# 应该看到以下文件:
# lstm_trajectory_model.h5          - LSTM航迹预测模型
# tactical_classifier_model.h5      - 战术分类器模型
# tactical_classifier_model.labels.npy - 分类器标签文件
```

#### 🔧 加载模型的基本方法

```python
import tensorflow as tf
import numpy as np
from pathlib import Path

def load_models():
    """加载所有训练好的模型"""
    model_path = Path("data/models")

    # 检查模型文件是否存在
    lstm_path = model_path / "lstm_trajectory_model.h5"
    classifier_path = model_path / "tactical_classifier_model.h5"
    labels_path = model_path / "tactical_classifier_model.labels.npy"

    if not lstm_path.exists():
        print("❌ LSTM模型文件不存在，请先运行训练脚本")
        return None, None, None

    if not classifier_path.exists():
        print("❌ 分类器模型文件不存在，请先运行训练脚本")
        return None, None, None

    try:
        # 加载LSTM模型
        print("📥 加载LSTM航迹预测模型...")
        lstm_model = tf.keras.models.load_model(str(lstm_path))
        print(f"✅ LSTM模型加载成功，参数量: {lstm_model.count_params():,}")

        # 加载分类器模型
        print("📥 加载战术分类器模型...")
        classifier_model = tf.keras.models.load_model(str(classifier_path))
        print(f"✅ 分类器模型加载成功，参数量: {classifier_model.count_params():,}")

        # 加载分类标签
        if labels_path.exists():
            class_labels = np.load(str(labels_path), allow_pickle=True).item()
            print(f"✅ 分类标签加载成功，类别数: {len(class_labels)}")
        else:
            # 默认标签
            class_labels = {
                "巡逻任务": 0, "预警探测": 1, "电子侦察": 2,
                "电子干扰": 3, "对空攻击": 4, "对地攻击": 5
            }
            print("⚠️ 使用默认分类标签")

        return lstm_model, classifier_model, class_labels

    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return None, None, None

# 使用示例
lstm_model, classifier_model, class_labels = load_models()
```

#### 🎯 使用预测器类（推荐方法）

```python
from src.prediction.predictor import TrajectoryPredictor

# 创建预测器实例
predictor = TrajectoryPredictor()

# 加载模型
try:
    predictor.load_models(
        lstm_path="data/models/lstm_trajectory_model.h5",
        classifier_path="data/models/tactical_classifier_model.h5"
    )
    print("✅ 预测器初始化成功！")
except Exception as e:
    print(f"❌ 预测器初始化失败: {e}")
```

### 4.2 单目标航迹预测的完整示例

#### 🎯 基础预测示例

```python
from datetime import datetime
from src.data.radar_data import RadarSignal
from src.prediction.predictor import TrajectoryPredictor

def single_target_prediction_demo():
    """单目标航迹预测演示"""
    print("=== 单目标航迹预测演示 ===")

    # 1. 创建预测器
    predictor = TrajectoryPredictor()

    # 2. 加载模型
    predictor.load_models(
        lstm_path="data/models/lstm_trajectory_model.h5",
        classifier_path="data/models/tactical_classifier_model.h5"
    )

    # 3. 创建雷达信号数据
    signal = RadarSignal(
        timestamp=datetime.now(),
        station_id="radar_001",
        target_id="target_001",

        # 基本测量
        toa=0.15,
        pa=0.785,

        # 位置和运动
        position=(5000.0, 3000.0, 8000.0),
        position_accuracy=(10.0, 12.0, 20.0),
        velocity=(250.0, 150.0, 30.0),
        acceleration=(5.0, -2.0, 1.0),

        # 姿态
        heading=0.524,
        elevation=0.118,
        pitch=0.1, roll=-0.05, yaw=0.02,

        # 雷达参数
        target_distance=9434.0,
        pulse_width=1.2,
        radar_frequency=3000.0,
        prf=2500.0,
        signal_strength=0.85,
        communication_status=0,
        snr=20.5,
        confidence=0.92
    )

    # 4. 进行预测
    try:
        prediction_result = predictor.predict_single(signal)

        # 5. 显示预测结果
        print("\n📊 预测结果:")
        print(f"  🎯 目标ID: {prediction_result['target_id']}")

        # 航迹预测结果
        if 'trajectory_prediction' in prediction_result:
            traj_pred = prediction_result['trajectory_prediction']
            print(f"  📈 航迹预测: {len(traj_pred)}个未来时间点")
            print(f"  📍 下一个位置: {traj_pred[0][:3]}")  # 显示下一个位置
            print(f"  🏃 预测速度: {traj_pred[0][3:6]}")    # 显示预测速度

        # 战术分类结果
        if 'tactical_prediction' in prediction_result:
            tactical_pred = prediction_result['tactical_prediction']
            print(f"  🎖️ 战术模式: {tactical_pred['predicted_mode']}")
            print(f"  📊 置信度: {tactical_pred['confidence']:.3f}")

            # 显示所有类别的概率
            print("  📋 各模式概率:")
            for mode, prob in tactical_pred['probabilities'].items():
                print(f"    - {mode}: {prob:.3f}")

        return prediction_result

    except Exception as e:
        print(f"❌ 预测失败: {e}")
        return None

# 运行演示
result = single_target_prediction_demo()
```

**期望输出**:
```
=== 单目标航迹预测演示 ===
✅ 预测器初始化成功！

📊 预测结果:
  🎯 目标ID: target_001
  📈 航迹预测: 10个未来时间点
  📍 下一个位置: [5250.0, 3150.0, 8030.0]
  🏃 预测速度: [248.5, 152.3, 28.7]
  🎖️ 战术模式: 巡逻任务
  📊 置信度: 0.847
  📋 各模式概率:
    - 巡逻任务: 0.847
    - 预警探测: 0.089
    - 电子侦察: 0.034
    - 电子干扰: 0.012
    - 对空攻击: 0.011
    - 对地攻击: 0.007
```

#### 📈 时序预测示例

```python
def time_series_prediction_demo():
    """时序航迹预测演示"""
    print("=== 时序航迹预测演示 ===")

    # 创建一系列历史观测数据（20个时间点）
    import numpy as np
    from datetime import timedelta

    base_time = datetime.now()
    base_position = np.array([5000.0, 3000.0, 8000.0])
    base_velocity = np.array([200.0, 100.0, 20.0])

    historical_signals = []

    for i in range(20):  # 创建20个历史时间点
        # 模拟真实的航迹演化
        current_time = base_time + timedelta(seconds=i)
        current_position = base_position + base_velocity * i + np.random.normal(0, 10, 3)
        current_velocity = base_velocity + np.random.normal(0, 5, 3)

        signal = RadarSignal(
            timestamp=current_time,
            station_id="radar_001",
            target_id="target_sequence",
            toa=0.1 + i * 0.005,
            pa=np.pi/4 + i * 0.01,
            position=tuple(current_position),
            velocity=tuple(current_velocity),
            acceleration=(2.0, -1.0, 0.5),
            heading=np.pi/3 + i * 0.02,
            elevation=0.1,
            pitch=0.05, roll=0.02, yaw=0.01,
            target_distance=np.linalg.norm(current_position),
            pulse_width=1.0,
            radar_frequency=3000.0,
            prf=2000.0,
            signal_strength=0.8 + np.random.normal(0, 0.05),
            communication_status=0,
            snr=20.0 + np.random.normal(0, 2),
            confidence=0.9 + np.random.normal(0, 0.05)
        )

        historical_signals.append(signal)

    # 使用预测器进行时序预测
    predictor = TrajectoryPredictor()
    predictor.load_models(
        lstm_path="data/models/lstm_trajectory_model.h5",
        classifier_path="data/models/tactical_classifier_model.h5"
    )

    try:
        # 进行时序预测
        sequence_prediction = predictor.predict_sequence(historical_signals)

        print(f"📊 时序预测结果:")
        print(f"  📈 输入序列长度: {len(historical_signals)}")
        print(f"  🔮 预测时间步数: {len(sequence_prediction['future_trajectory'])}")

        # 显示未来几个时间点的预测
        future_points = sequence_prediction['future_trajectory'][:3]  # 显示前3个
        for i, point in enumerate(future_points):
            print(f"  📍 未来时刻{i+1}: 位置{point[:3]}, 速度{point[3:6]}")

        return sequence_prediction

    except Exception as e:
        print(f"❌ 时序预测失败: {e}")
        return None

# 运行时序预测演示
sequence_result = time_series_prediction_demo()
```

### 4.3 多站点协同观测的使用方法

#### 🌐 多站点融合预测

```python
from src.data.multi_station_fusion import MultiStationDataFusion, AWACSTrajectoryPredictor

def multi_station_prediction_demo():
    """多站点协同观测预测演示"""
    print("=== 多站点协同观测演示 ===")

    # 1. 创建多站点融合引擎
    fusion_engine = MultiStationDataFusion(
        time_window=0.1,    # 100ms时间窗口
        min_stations=2      # 最少2个站点
    )

    # 2. 添加雷达站点
    from src.data.radar_data import RadarStation

    stations = [
        RadarStation("radar_001", "东部站点", (0.0, 0.0, 100.0), 200.0, 3000.0),
        RadarStation("radar_002", "西部站点", (150.0, 50.0, 120.0), 180.0, 3200.0),
        RadarStation("radar_003", "南部站点", (75.0, -100.0, 110.0), 190.0, 2800.0)
    ]

    for station in stations:
        fusion_engine.add_station(station)
        print(f"✅ 添加站点: {station.name}")

    # 3. 创建同一目标的多站点观测数据
    base_time = datetime.now()
    target_id = "multi_target_001"

    # 模拟三个站点同时观测同一目标
    multi_station_signals = []

    for i, station in enumerate(stations):
        # 每个站点的观测略有不同（模拟测量误差）
        time_offset = i * 0.03  # 30ms间隔
        position_noise = np.random.normal(0, 5, 3)  # 位置测量噪声

        signal = RadarSignal(
            timestamp=base_time + timedelta(seconds=time_offset),
            station_id=station.station_id,
            target_id=target_id,
            toa=0.15 + i * 0.01,
            pa=0.785 + i * 0.02,
            position=tuple(np.array([5000.0, 3000.0, 8000.0]) + position_noise),
            velocity=(250.0 + np.random.normal(0, 2), 150.0 + np.random.normal(0, 2), 30.0),
            acceleration=(5.0, -2.0, 1.0),
            heading=0.524,
            elevation=0.118,
            pitch=0.1, roll=-0.05, yaw=0.02,
            target_distance=9434.0 + np.random.normal(0, 10),
            pulse_width=1.2,
            radar_frequency=station.frequency,
            prf=2500.0,
            signal_strength=0.85 + np.random.normal(0, 0.05),
            communication_status=0,
            snr=20.5 + np.random.normal(0, 1),
            confidence=0.92 + np.random.normal(0, 0.02)
        )

        fusion_engine.add_signal(signal)
        multi_station_signals.append(signal)
        print(f"📡 添加{station.name}观测数据")

    # 4. 创建融合航迹
    fused_trajectory = fusion_engine.create_fused_trajectory(target_id)

    if fused_trajectory:
        print(f"\n✅ 多站点数据融合成功!")
        print(f"  📊 融合信号数: {len(fused_trajectory.signals)}")
        print(f"  🎯 融合置信度: {fused_trajectory.signals[0].confidence:.3f}")

        # 显示融合前后的对比
        print(f"\n📋 融合前后对比:")
        print(f"  原始观测数: {len(multi_station_signals)}")
        for i, signal in enumerate(multi_station_signals):
            print(f"    站点{i+1}: 位置{signal.position}, 置信度{signal.confidence:.3f}")

        fused_signal = fused_trajectory.signals[0]
        print(f"  融合结果: 位置{fused_signal.position}, 置信度{fused_signal.confidence:.3f}")

        return fused_trajectory
    else:
        print("❌ 多站点数据融合失败")
        return None

# 运行多站点演示
multi_result = multi_station_prediction_demo()
```

### 4.4 预警机探测模式识别的应用

#### 🎯 预警机模式识别

```python
def awacs_detection_mode_demo():
    """预警机探测模式识别演示"""
    print("=== 预警机探测模式识别演示 ===")

    # 1. 创建多站点融合引擎
    fusion_engine = MultiStationDataFusion(time_window=0.1, min_stations=2)

    # 2. 创建预警机预测器
    awacs_predictor = AWACSTrajectoryPredictor(fusion_engine)

    # 3. 模拟预警机的三种探测模式数据

    # 机械扫描模式：规律的圆形扫描
    print("📡 模拟机械扫描模式...")
    mechanical_signals = []
    base_time = datetime.now()

    for t in range(20):
        signal = RadarSignal(
            timestamp=base_time + timedelta(seconds=t),
            station_id="radar_001",
            target_id="awacs_mechanical",
            toa=0.1 + t * 0.005 + np.random.normal(0, 0.001),  # 规律变化
            pa=np.pi/3 + t * 0.01 + np.random.normal(0, 0.002),  # 规律变化
            position=(10000 + t * 50, 8000 + t * 30, 12000),
            velocity=(120, 80, 5),
            acceleration=(0.5, 0.2, 0.1),
            heading=0.785,
            elevation=0.087,
            pitch=0.02, roll=-0.01, yaw=0.005,
            target_distance=15000 + t * 10,
            pulse_width=2.0,
            radar_frequency=2800.0,
            prf=1500.0,
            signal_strength=0.75,
            communication_status=0,
            snr=18.0 + np.random.normal(0, 1),
            confidence=0.88 + np.random.normal(0, 0.02)
        )
        fusion_engine.add_signal(signal)
        mechanical_signals.append(signal)

    # 4. 进行预警机状态预测
    try:
        awacs_result = awacs_predictor.predict_awacs_state("awacs_mechanical")

        print(f"\n🎯 预警机状态预测结果:")
        print(f"  📡 目标ID: {awacs_result['target_id']}")
        print(f"  🔍 预测探测模式: {awacs_result['predicted_mode']}")
        print(f"  📊 模式概率分布:")

        for mode, prob in awacs_result['detection_mode_probabilities'].items():
            print(f"    - {mode}: {prob:.3f}")

        print(f"  🎯 融合置信度: {awacs_result['fusion_confidence']:.3f}")
        print(f"  📍 参与站点数: {awacs_result['station_count']}")
        print(f"  📈 时序样本数: {awacs_result['sample_count']}")

        return awacs_result

    except Exception as e:
        print(f"❌ 预警机状态预测失败: {e}")
        return None

# 运行预警机演示
awacs_result = awacs_detection_mode_demo()
```

**期望输出**:
```
=== 预警机探测模式识别演示 ===
📡 模拟机械扫描模式...

🎯 预警机状态预测结果:
  📡 目标ID: awacs_mechanical
  🔍 预测探测模式: 机械扫描
  📊 模式概率分布:
    - 机械扫描: 0.942
    - 扇面扫描: 0.035
    - 引导拦截: 0.023
  🎯 融合置信度: 0.902
  📍 参与站点数: 1
  📈 时序样本数: 1
```

---

## 🔌 5. API接口使用指南

### 5.1 REST API的启动和配置

#### 🚀 启动REST API服务

```bash
# 方法1: 直接运行API服务
python src/api/rest_api.py

# 方法2: 使用uvicorn启动（推荐）
uvicorn src.api.rest_api:app --host 0.0.0.0 --port 8000 --reload
```

**成功启动后的输出**:
```
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [12345] using statreload
INFO:     Started server process [12346]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
```

#### 🌐 访问API文档

启动服务后，可以通过浏览器访问：

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **健康检查**: http://localhost:8000/health

#### ⚙️ API配置

编辑 `.env` 文件配置API参数：

```bash
# API配置
API_HOST=0.0.0.0        # 监听地址
API_PORT=8000           # 监听端口
WEBSOCKET_PORT=8001     # WebSocket端口

# 模型配置
MODEL_PATH=data/models
LSTM_MODEL_FILE=lstm_trajectory_model.h5
CLASSIFIER_MODEL_FILE=tactical_classifier_model.h5

# 性能配置
MAX_CONCURRENT_REQUESTS=10
REQUEST_TIMEOUT=30
```

### 5.2 REST API调用示例

#### 📡 添加雷达信号

**API端点**: `POST /radar/signal`

```python
import requests
import json
from datetime import datetime

def add_radar_signal_example():
    """添加雷达信号的API调用示例"""

    # API服务地址
    api_url = "http://localhost:8000"

    # 构造雷达信号数据
    signal_data = {
        "timestamp": datetime.now().isoformat() + "Z",
        "station_id": "radar_001",
        "target_id": "target_001",
        "toa": 0.15,
        "pa": 0.785,
        "position": [5000.0, 3000.0, 8000.0],
        "position_accuracy": [10.0, 12.0, 20.0],
        "velocity": [250.0, 150.0, 30.0],
        "acceleration": [5.0, -2.0, 1.0],
        "heading": 0.524,
        "elevation": 0.118,
        "pitch": 0.1,
        "roll": -0.05,
        "yaw": 0.02,
        "target_distance": 9434.0,
        "pulse_width": 1.2,
        "radar_frequency": 3000.0,
        "prf": 2500.0,
        "signal_strength": 0.85,
        "communication_status": 0,
        "snr": 20.5,
        "confidence": 0.92
    }

    try:
        # 发送POST请求
        response = requests.post(
            f"{api_url}/radar/signal",
            json=signal_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )

        if response.status_code == 200:
            result = response.json()
            print("✅ 雷达信号添加成功!")
            print(f"📊 响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return result
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return None

    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
        return None

# 运行示例
add_result = add_radar_signal_example()
```

**期望响应**:
```json
{
  "status": "success",
  "message": "雷达信号添加成功",
  "signal_id": "signal_12345",
  "timestamp": "2025-09-03T04:16:00.123456Z"
}
```

#### 🎯 获取航迹预测

**API端点**: `GET /prediction/trajectory/{target_id}`

```python
def get_trajectory_prediction_example(target_id="target_001"):
    """获取航迹预测的API调用示例"""

    api_url = "http://localhost:8000"

    try:
        # 发送GET请求
        response = requests.get(
            f"{api_url}/prediction/trajectory/{target_id}",
            timeout=10
        )

        if response.status_code == 200:
            result = response.json()
            print(f"✅ 航迹预测获取成功!")
            print(f"🎯 目标ID: {result['target_id']}")
            print(f"📈 预测点数: {len(result['trajectory_prediction'])}")
            print(f"📍 下一个位置: {result['trajectory_prediction'][0][:3]}")
            print(f"🎖️ 战术模式: {result['tactical_prediction']['predicted_mode']}")
            print(f"📊 置信度: {result['tactical_prediction']['confidence']:.3f}")
            return result
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return None

    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
        return None

# 运行示例
prediction_result = get_trajectory_prediction_example()
```

#### 🌐 多站点融合预测

**API端点**: `POST /prediction/multi-station`

```python
def multi_station_prediction_api_example():
    """多站点融合预测的API调用示例"""

    api_url = "http://localhost:8000"

    # 构造多站点数据
    multi_station_data = {
        "target_id": "multi_target_001",
        "time_window": 0.1,
        "min_stations": 2,
        "signals": [
            {
                "timestamp": datetime.now().isoformat() + "Z",
                "station_id": "radar_001",
                "target_id": "multi_target_001",
                "toa": 0.15,
                "pa": 0.785,
                "position": [5000.0, 3000.0, 8000.0],
                "velocity": [250.0, 150.0, 30.0],
                "snr": 22.0,
                "confidence": 0.92
            },
            {
                "timestamp": (datetime.now() + timedelta(milliseconds=50)).isoformat() + "Z",
                "station_id": "radar_002",
                "target_id": "multi_target_001",
                "toa": 0.12,
                "pa": 0.820,
                "position": [5020.0, 2980.0, 8010.0],
                "velocity": [248.0, 152.0, 28.0],
                "snr": 20.5,
                "confidence": 0.89
            },
            {
                "timestamp": (datetime.now() + timedelta(milliseconds=80)).isoformat() + "Z",
                "station_id": "radar_003",
                "target_id": "multi_target_001",
                "toa": 0.18,
                "pa": 0.750,
                "position": [4980.0, 3020.0, 7990.0],
                "velocity": [252.0, 148.0, 32.0],
                "snr": 24.2,
                "confidence": 0.94
            }
        ]
    }

    try:
        # 发送POST请求
        response = requests.post(
            f"{api_url}/prediction/multi-station",
            json=multi_station_data,
            headers={"Content-Type": "application/json"},
            timeout=15
        )

        if response.status_code == 200:
            result = response.json()
            print("✅ 多站点融合预测成功!")
            print(f"🎯 目标ID: {result['target_id']}")
            print(f"📡 参与站点数: {result['station_count']}")
            print(f"🎯 融合置信度: {result['fusion_confidence']:.3f}")
            print(f"📈 融合航迹点数: {len(result['fused_trajectory'])}")
            return result
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return None

    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
        return None

# 运行示例
multi_station_result = multi_station_prediction_api_example()
```

### 5.3 WebSocket实时数据流的使用

#### 🔄 启动WebSocket服务

```bash
# 启动WebSocket服务
python src/api/websocket_api.py
```

**成功启动后的输出**:
```
INFO:     Uvicorn running on http://0.0.0.0:8001 (Press CTRL+C to quit)
INFO:     WebSocket服务启动成功，监听端口: 8001
```

#### 📡 WebSocket客户端示例

```python
import asyncio
import websockets
import json
from datetime import datetime

async def websocket_client_example():
    """WebSocket客户端示例"""

    uri = "ws://localhost:8001/ws"

    try:
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket连接成功!")

            # 发送雷达信号数据
            signal_message = {
                "type": "radar_signal",
                "data": {
                    "timestamp": datetime.now().isoformat() + "Z",
                    "station_id": "radar_001",
                    "target_id": "ws_target_001",
                    "toa": 0.15,
                    "pa": 0.785,
                    "position": [5000.0, 3000.0, 8000.0],
                    "velocity": [250.0, 150.0, 30.0],
                    "acceleration": [5.0, -2.0, 1.0],
                    "heading": 0.524,
                    "snr": 20.5,
                    "confidence": 0.92
                }
            }

            # 发送数据
            await websocket.send(json.dumps(signal_message))
            print("📤 发送雷达信号数据")

            # 接收响应
            response = await websocket.recv()
            response_data = json.loads(response)

            print("📥 收到响应:")
            print(f"  状态: {response_data.get('status')}")
            print(f"  消息: {response_data.get('message')}")

            # 持续接收实时预测结果
            print("🔄 等待实时预测结果...")

            timeout_count = 0
            while timeout_count < 10:  # 最多等待10次
                try:
                    # 设置超时时间
                    prediction = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                    prediction_data = json.loads(prediction)

                    if prediction_data.get('type') == 'prediction_result':
                        print("🎯 收到预测结果:")
                        data = prediction_data['data']
                        print(f"  目标ID: {data['target_id']}")
                        print(f"  战术模式: {data.get('tactical_mode', 'N/A')}")
                        print(f"  置信度: {data.get('confidence', 0):.3f}")
                        break

                except asyncio.TimeoutError:
                    timeout_count += 1
                    print(f"⏰ 等待中... ({timeout_count}/10)")

            print("🔚 WebSocket演示完成")

    except Exception as e:
        print(f"❌ WebSocket连接失败: {e}")

# 运行WebSocket客户端
# asyncio.run(websocket_client_example())
```

#### 📊 批量数据发送

```python
async def websocket_batch_example():
    """WebSocket批量数据发送示例"""

    uri = "ws://localhost:8001/ws"

    try:
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket连接成功!")

            # 构造批量信号数据
            batch_signals = []
            base_time = datetime.now()

            for i in range(5):  # 发送5个信号
                signal = {
                    "timestamp": (base_time + timedelta(seconds=i)).isoformat() + "Z",
                    "station_id": f"radar_{i+1:03d}",
                    "target_id": "batch_target_001",
                    "toa": 0.1 + i * 0.01,
                    "pa": 0.5 + i * 0.1,
                    "position": [5000 + i * 100, 3000 + i * 50, 8000],
                    "velocity": [200 + i * 10, 150, 30],
                    "snr": 20 + i,
                    "confidence": 0.9 - i * 0.02
                }
                batch_signals.append(signal)

            # 发送批量数据
            batch_message = {
                "type": "batch_signals",
                "data": batch_signals
            }

            await websocket.send(json.dumps(batch_message))
            print(f"📤 发送批量数据: {len(batch_signals)}个信号")

            # 接收批量处理结果
            response = await websocket.recv()
            response_data = json.loads(response)

            print("📥 批量处理结果:")
            print(f"  处理状态: {response_data.get('status')}")
            print(f"  处理数量: {response_data.get('processed_count', 0)}")
            print(f"  成功数量: {response_data.get('success_count', 0)}")
            print(f"  失败数量: {response_data.get('error_count', 0)}")

    except Exception as e:
        print(f"❌ 批量数据发送失败: {e}")

# 运行批量发送示例
# asyncio.run(websocket_batch_example())
```

### 5.4 错误处理和调试方法

#### 🚨 常见错误及解决方案

**1. 连接拒绝错误**
```
ConnectionRefusedError: [Errno 61] Connection refused
```

**解决方案**:
```bash
# 检查API服务是否启动
curl http://localhost:8000/health

# 如果没有响应，启动API服务
python src/api/rest_api.py
```

**2. 模型加载失败**
```
FileNotFoundError: [Errno 2] No such file or directory: 'data/models/lstm_trajectory_model.h5'
```

**解决方案**:
```bash
# 检查模型文件是否存在
ls -la data/models/

# 如果不存在，运行训练脚本
python scripts/simple_train.py
python scripts/train_classifier.py
```

**3. 数据验证失败**
```json
{
  "error": {
    "type": "ValidationError",
    "field": "toa",
    "message": "TOA值超出有效范围 [0.001, 1.0]"
  }
}
```

**解决方案**:
```python
# 检查数据范围
def validate_signal_data(data):
    """验证信号数据"""
    errors = []

    if not (0.001 <= data.get('toa', 0) <= 1.0):
        errors.append("TOA值必须在0.001-1.0之间")

    if not (0.0 <= data.get('pa', 0) <= 2 * 3.14159):
        errors.append("PA值必须在0-2π之间")

    return errors

# 使用验证函数
errors = validate_signal_data(signal_data)
if errors:
    print("数据验证失败:", errors)
```

#### 🔍 调试技巧

**1. 启用详细日志**
```python
import logging

# 设置日志级别
logging.basicConfig(level=logging.DEBUG)

# 查看API请求详情
import requests
import http.client as http_client

http_client.HTTPConnection.debuglevel = 1
```

**2. 使用curl测试API**
```bash
# 测试健康检查
curl -X GET http://localhost:8000/health

# 测试添加信号
curl -X POST http://localhost:8000/radar/signal \
  -H "Content-Type: application/json" \
  -d '{
    "timestamp": "2025-09-03T04:16:00.000Z",
    "station_id": "radar_001",
    "target_id": "test_001",
    "toa": 0.15,
    "pa": 0.785,
    "position": [5000, 3000, 8000],
    "velocity": [250, 150, 30]
  }'
```

**3. 监控API性能**
```python
import time

def monitor_api_performance():
    """监控API性能"""
    start_time = time.time()

    response = requests.get("http://localhost:8000/prediction/trajectory/target_001")

    end_time = time.time()
    response_time = end_time - start_time

    print(f"API响应时间: {response_time:.3f}秒")
    print(f"响应状态: {response.status_code}")
    print(f"响应大小: {len(response.content)}字节")

# 运行性能监控
monitor_api_performance()
```

---

## 🎬 6. 实际应用场景演示

### 6.1 从零开始的完整使用流程

#### 🚀 完整演示脚本

创建一个完整的演示脚本 `complete_demo.py`：

```python
"""
AirModel_V0 完整使用流程演示
从环境检查到实际预测的完整过程
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta
import numpy as np

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def step1_environment_check():
    """步骤1: 环境检查"""
    print("🔍 步骤1: 环境检查")
    print("=" * 50)

    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")

    if python_version < (3, 8):
        print("❌ Python版本过低，需要3.8或更高版本")
        return False
    else:
        print("✅ Python版本符合要求")

    # 检查必要的包
    required_packages = ['tensorflow', 'numpy', 'pandas', 'matplotlib']
    missing_packages = []

    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"❌ {package} 未安装")
            missing_packages.append(package)

    if missing_packages:
        print(f"请安装缺失的包: pip install {' '.join(missing_packages)}")
        return False

    # 检查GPU
    try:
        import tensorflow as tf
        gpus = tf.config.list_physical_devices('GPU')
        if gpus:
            print(f"✅ 检测到GPU: {len(gpus)}个")
        else:
            print("⚠️ 未检测到GPU，将使用CPU模式")
    except:
        print("⚠️ 无法检测GPU状态")

    print("✅ 环境检查完成\n")
    return True

def step2_model_training():
    """步骤2: 模型训练"""
    print("🎓 步骤2: 模型训练")
    print("=" * 50)

    model_dir = Path("data/models")
    lstm_model_path = model_dir / "lstm_trajectory_model.h5"
    classifier_model_path = model_dir / "tactical_classifier_model.h5"

    # 检查模型是否已存在
    if lstm_model_path.exists() and classifier_model_path.exists():
        print("✅ 发现已训练的模型，跳过训练步骤")
        return True

    print("📚 开始训练模型...")

    try:
        # 训练LSTM模型
        print("🔄 训练LSTM航迹预测模型...")
        os.system("python scripts/simple_train.py")

        if lstm_model_path.exists():
            print("✅ LSTM模型训练完成")
        else:
            print("❌ LSTM模型训练失败")
            return False

        # 训练分类器
        print("🔄 训练战术分类器...")
        os.system("python scripts/train_classifier.py")

        if classifier_model_path.exists():
            print("✅ 分类器训练完成")
        else:
            print("❌ 分类器训练失败")
            return False

        print("✅ 所有模型训练完成\n")
        return True

    except Exception as e:
        print(f"❌ 训练过程出错: {e}")
        return False

def step3_single_prediction():
    """步骤3: 单目标预测演示"""
    print("🎯 步骤3: 单目标预测演示")
    print("=" * 50)

    try:
        from src.prediction.predictor import TrajectoryPredictor
        from src.data.radar_data import RadarSignal

        # 创建预测器
        predictor = TrajectoryPredictor()

        # 加载模型
        predictor.load_models(
            lstm_path="data/models/lstm_trajectory_model.h5",
            classifier_path="data/models/tactical_classifier_model.h5"
        )
        print("✅ 模型加载成功")

        # 创建测试信号
        test_signal = RadarSignal(
            timestamp=datetime.now(),
            station_id="radar_001",
            target_id="demo_target",
            toa=0.15,
            pa=0.785,
            position=(5000.0, 3000.0, 8000.0),
            position_accuracy=(10.0, 12.0, 20.0),
            velocity=(250.0, 150.0, 30.0),
            acceleration=(5.0, -2.0, 1.0),
            heading=0.524,
            elevation=0.118,
            pitch=0.1, roll=-0.05, yaw=0.02,
            target_distance=9434.0,
            pulse_width=1.2,
            radar_frequency=3000.0,
            prf=2500.0,
            signal_strength=0.85,
            communication_status=0,
            snr=20.5,
            confidence=0.92
        )

        # 进行预测
        result = predictor.predict_single(test_signal)

        print("📊 预测结果:")
        print(f"  🎯 目标ID: {result['target_id']}")

        if 'trajectory_prediction' in result:
            traj = result['trajectory_prediction']
            print(f"  📈 预测轨迹点数: {len(traj)}")
            print(f"  📍 下一个位置: {traj[0][:3]}")

        if 'tactical_prediction' in result:
            tactical = result['tactical_prediction']
            print(f"  🎖️ 战术模式: {tactical['predicted_mode']}")
            print(f"  📊 置信度: {tactical['confidence']:.3f}")

        print("✅ 单目标预测演示完成\n")
        return True

    except Exception as e:
        print(f"❌ 单目标预测失败: {e}")
        return False

def step4_multi_station_demo():
    """步骤4: 多站点协同演示"""
    print("🌐 步骤4: 多站点协同演示")
    print("=" * 50)

    try:
        from src.data.multi_station_fusion import MultiStationDataFusion
        from src.data.radar_data import RadarStation, RadarSignal

        # 创建融合引擎
        fusion_engine = MultiStationDataFusion(time_window=0.1, min_stations=2)

        # 添加站点
        stations = [
            RadarStation("radar_001", "东部站点", (0, 0, 100), 200, 3000),
            RadarStation("radar_002", "西部站点", (150, 50, 120), 180, 3200),
            RadarStation("radar_003", "南部站点", (75, -100, 110), 190, 2800)
        ]

        for station in stations:
            fusion_engine.add_station(station)

        print(f"✅ 添加了{len(stations)}个雷达站点")

        # 创建多站点观测数据
        base_time = datetime.now()
        target_id = "multi_demo_target"

        for i, station in enumerate(stations):
            signal = RadarSignal(
                timestamp=base_time + timedelta(milliseconds=i*30),
                station_id=station.station_id,
                target_id=target_id,
                toa=0.15 + i * 0.01,
                pa=0.785 + i * 0.02,
                position=(5000 + i*10, 3000 + i*5, 8000),
                velocity=(250, 150, 30),
                acceleration=(5, -2, 1),
                heading=0.524,
                elevation=0.118,
                pitch=0.1, roll=-0.05, yaw=0.02,
                target_distance=9434 + i*10,
                pulse_width=1.2,
                radar_frequency=station.frequency,
                prf=2500,
                signal_strength=0.85,
                communication_status=0,
                snr=20.5 + i,
                confidence=0.92 - i*0.01
            )

            fusion_engine.add_signal(signal)

        # 创建融合航迹
        fused_trajectory = fusion_engine.create_fused_trajectory(target_id)

        if fused_trajectory:
            print("✅ 多站点数据融合成功")
            print(f"  📊 融合信号数: {len(fused_trajectory.signals)}")
            print(f"  🎯 融合置信度: {fused_trajectory.signals[0].confidence:.3f}")
        else:
            print("❌ 多站点数据融合失败")
            return False

        print("✅ 多站点协同演示完成\n")
        return True

    except Exception as e:
        print(f"❌ 多站点协同演示失败: {e}")
        return False

def step5_api_demo():
    """步骤5: API接口演示"""
    print("🔌 步骤5: API接口演示")
    print("=" * 50)

    import subprocess
    import time
    import requests

    try:
        # 启动API服务（后台运行）
        print("🚀 启动API服务...")
        api_process = subprocess.Popen([
            sys.executable, "src/api/rest_api.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)

        # 等待服务启动
        time.sleep(5)

        # 测试健康检查
        try:
            response = requests.get("http://localhost:8000/health", timeout=5)
            if response.status_code == 200:
                print("✅ API服务启动成功")
            else:
                print("❌ API服务响应异常")
                return False
        except requests.exceptions.RequestException:
            print("❌ 无法连接到API服务")
            return False

        # 测试添加信号
        signal_data = {
            "timestamp": datetime.now().isoformat() + "Z",
            "station_id": "radar_001",
            "target_id": "api_demo_target",
            "toa": 0.15,
            "pa": 0.785,
            "position": [5000, 3000, 8000],
            "velocity": [250, 150, 30],
            "snr": 20.5,
            "confidence": 0.92
        }

        response = requests.post(
            "http://localhost:8000/radar/signal",
            json=signal_data,
            timeout=10
        )

        if response.status_code == 200:
            print("✅ API信号添加成功")
        else:
            print(f"❌ API信号添加失败: {response.status_code}")

        print("✅ API接口演示完成")

        # 停止API服务
        api_process.terminate()
        api_process.wait()

        print("🔚 API服务已停止\n")
        return True

    except Exception as e:
        print(f"❌ API演示失败: {e}")
        return False

def main():
    """主演示函数"""
    print("🚀 AirModel_V0 完整使用流程演示")
    print("=" * 60)
    print("本演示将引导您完成从环境检查到实际应用的完整流程")
    print("=" * 60)

    steps = [
        ("环境检查", step1_environment_check),
        ("模型训练", step2_model_training),
        ("单目标预测", step3_single_prediction),
        ("多站点协同", step4_multi_station_demo),
        ("API接口", step5_api_demo)
    ]

    success_count = 0

    for step_name, step_func in steps:
        print(f"\n{'='*20} {step_name} {'='*20}")

        try:
            if step_func():
                success_count += 1
                print(f"✅ {step_name} 完成")
            else:
                print(f"❌ {step_name} 失败")
                break
        except KeyboardInterrupt:
            print(f"\n⚠️ 用户中断了演示")
            break
        except Exception as e:
            print(f"❌ {step_name} 出现异常: {e}")
            break

    print("\n" + "="*60)
    print("🎉 演示完成总结")
    print("="*60)
    print(f"✅ 成功完成: {success_count}/{len(steps)} 个步骤")

    if success_count == len(steps):
        print("🎊 恭喜！您已成功完成AirModel_V0的完整使用流程")
        print("💡 现在您可以:")
        print("  - 使用训练好的模型进行航迹预测")
        print("  - 通过API接口集成到您的系统中")
        print("  - 运行examples/目录下的其他演示程序")
    else:
        print("⚠️ 部分步骤未完成，请检查错误信息并重试")

    print("\n📚 更多资源:")
    print("  - 详细文档: docs/")
    print("  - 演示程序: examples/")
    print("  - 技术支持: README.md")

if __name__ == "__main__":
    main()
```

#### 🎮 运行完整演示

```bash
# 运行完整演示
python complete_demo.py
```

### 6.2 常见问题和解决方案

#### ❓ 常见问题FAQ

**Q1: 训练过程中出现内存不足错误**
```
ResourceExhaustedError: OOM when allocating tensor
```

**A1: 解决方案**
```python
# 方法1: 减少批大小
batch_size = 16  # 从32减少到16

# 方法2: 启用GPU内存增长
import tensorflow as tf
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    tf.config.experimental.set_memory_growth(gpus[0], True)

# 方法3: 限制GPU内存使用
tf.config.experimental.set_virtual_device_configuration(
    gpus[0],
    [tf.config.experimental.VirtualDeviceConfiguration(memory_limit=2048)]
)
```

**Q2: 模型预测结果不准确**
```
预测置信度很低，或者预测结果明显错误
```

**A2: 解决方案**
```python
# 1. 检查输入数据范围
def check_data_range(signal):
    issues = []
    if not (0.001 <= signal.toa <= 1.0):
        issues.append(f"TOA超出范围: {signal.toa}")
    if not (0 <= signal.pa <= 2*np.pi):
        issues.append(f"PA超出范围: {signal.pa}")
    return issues

# 2. 增加训练数据量
# 在训练脚本中增加num_samples参数
X, y = generate_sample_data(num_samples=2000)  # 从1000增加到2000

# 3. 调整模型参数
# 增加训练轮数
epochs = 50  # 从20增加到50
```

**Q3: API服务启动失败**
```
Address already in use: 8000
```

**A3: 解决方案**
```bash
# 方法1: 查找占用端口的进程
lsof -i :8000  # Linux/macOS
netstat -ano | findstr :8000  # Windows

# 方法2: 杀死占用进程
kill -9 <PID>  # Linux/macOS
taskkill /PID <PID> /F  # Windows

# 方法3: 使用不同端口
uvicorn src.api.rest_api:app --port 8001
```

**Q4: 模型文件损坏或无法加载**
```
ValueError: Unable to load model
```

**A4: 解决方案**
```bash
# 1. 删除损坏的模型文件
rm data/models/*.h5

# 2. 重新训练模型
python scripts/simple_train.py
python scripts/train_classifier.py

# 3. 验证模型文件
python -c "import tensorflow as tf; model = tf.keras.models.load_model('data/models/lstm_trajectory_model.h5'); print('模型加载成功')"
```

### 6.3 性能优化建议

#### ⚡ 系统性能优化

**1. GPU加速配置**
```python
import tensorflow as tf

# 启用混合精度训练
policy = tf.keras.mixed_precision.Policy('mixed_float16')
tf.keras.mixed_precision.set_global_policy(policy)

# 优化GPU内存使用
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    for gpu in gpus:
        tf.config.experimental.set_memory_growth(gpu, True)
```

**2. 数据预处理优化**
```python
# 使用tf.data进行数据预处理
def create_optimized_dataset(X, y, batch_size=32):
    dataset = tf.data.Dataset.from_tensor_slices((X, y))
    dataset = dataset.batch(batch_size)
    dataset = dataset.prefetch(tf.data.AUTOTUNE)
    dataset = dataset.cache()  # 缓存数据
    return dataset
```

**3. 模型推理优化**
```python
# 模型量化
converter = tf.lite.TFLiteConverter.from_saved_model("data/models/lstm_model")
converter.optimizations = [tf.lite.Optimize.DEFAULT]
tflite_model = converter.convert()

# 保存量化模型
with open('data/models/lstm_model_quantized.tflite', 'wb') as f:
    f.write(tflite_model)
```

#### 📊 监控和调优

**1. 性能监控脚本**
```python
import time
import psutil
import tensorflow as tf

def monitor_system_performance():
    """监控系统性能"""
    print("=== 系统性能监控 ===")

    # CPU使用率
    cpu_percent = psutil.cpu_percent(interval=1)
    print(f"CPU使用率: {cpu_percent}%")

    # 内存使用
    memory = psutil.virtual_memory()
    print(f"内存使用: {memory.percent}% ({memory.used/1024/1024/1024:.1f}GB/{memory.total/1024/1024/1024:.1f}GB)")

    # GPU使用（如果有）
    try:
        gpus = tf.config.experimental.list_physical_devices('GPU')
        if gpus:
            print(f"GPU数量: {len(gpus)}")
    except:
        print("无法检测GPU状态")

    # 磁盘使用
    disk = psutil.disk_usage('.')
    print(f"磁盘使用: {disk.percent}% ({disk.used/1024/1024/1024:.1f}GB/{disk.total/1024/1024/1024:.1f}GB)")

# 运行监控
monitor_system_performance()
```

**2. 预测性能基准测试**
```python
def benchmark_prediction_performance():
    """预测性能基准测试"""
    from src.prediction.predictor import TrajectoryPredictor

    predictor = TrajectoryPredictor()
    predictor.load_models(
        lstm_path="data/models/lstm_trajectory_model.h5",
        classifier_path="data/models/tactical_classifier_model.h5"
    )

    # 创建测试数据
    test_signals = [create_test_signal() for _ in range(100)]

    # 性能测试
    start_time = time.time()

    for signal in test_signals:
        result = predictor.predict_single(signal)

    end_time = time.time()

    total_time = end_time - start_time
    avg_time = total_time / len(test_signals)

    print(f"性能基准测试结果:")
    print(f"  总测试样本: {len(test_signals)}")
    print(f"  总耗时: {total_time:.3f}秒")
    print(f"  平均预测时间: {avg_time*1000:.1f}毫秒")
    print(f"  预测吞吐量: {len(test_signals)/total_time:.1f} predictions/second")

# 运行基准测试
benchmark_prediction_performance()
```

---

## 🎉 总结

恭喜您完成了AirModel_V0的完整学习之旅！通过本指南，您已经掌握了：

### ✅ 已掌握的技能

1. **环境配置**: Python环境、依赖安装、GPU配置
2. **数据理解**: 27维特征向量的含义和应用
3. **数据处理**: 雷达信号创建、多站点融合、预处理流程
4. **模型训练**: LSTM和分类器的完整训练流程
5. **模型使用**: 单目标预测、多站点协同、预警机识别
6. **API集成**: REST API和WebSocket的使用方法
7. **问题解决**: 常见问题的诊断和解决方案

### 🚀 下一步建议

1. **深入实践**: 运行更多examples/目录下的演示程序
2. **自定义数据**: 使用您自己的雷达数据进行训练和预测
3. **性能优化**: 根据实际需求调优模型参数
4. **系统集成**: 将AirModel_V0集成到您的实际系统中
5. **持续学习**: 关注项目更新，学习新功能

### 📚 参考资源

- **详细技术文档**: `docs/ENHANCED_MODEL_GUIDE.md`
- **API接口文档**: `docs/api.md`
- **输入数据规范**: `INPUT_DATA_SPECIFICATION.md`
- **部署指南**: `docs/deployment.md`

### 💬 技术支持

如果在使用过程中遇到问题：

1. 查看项目README.md文档
2. 运行examples/目录下的相关演示
3. 检查logs/目录下的日志文件
4. 参考本指南的常见问题部分

**祝您使用愉快！AirModel_V0将为您的航迹预测和战术识别任务提供强大支持！** 🎯
```