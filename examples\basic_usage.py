"""
基本使用示例
演示如何使用航迹预测模型
"""

import sys
from pathlib import Path
from datetime import datetime
import numpy as np

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.prediction.predictor import TrajectoryPredictor
from src.data.radar_data import RadarSignal, TargetTrajectory, RadarStation
from config.logging_config import setup_logging

logger = setup_logging()


def create_sample_trajectory() -> TargetTrajectory:
    """创建示例航迹数据"""
    
    # 创建示例雷达信号
    signals = []
    base_time = datetime.now()
    
    for i in range(25):  # 创建25个数据点
        signal = RadarSignal(
            timestamp=base_time + timedelta(seconds=i),
            station_id="radar_001",
            target_id="target_001",
            toa=0.1 + i * 0.01,  # 模拟TOA数据
            pa=np.pi/4 + i * 0.02,  # 模拟PA数据
            position=(100 + i * 10, 200 + i * 5, 1000 + i * 2),  # 模拟位置
            velocity=(150, 50, 10),  # 模拟速度
            heading=np.pi/6,  # 航向角
            elevation=np.pi/12,  # 高度角
            pitch=0.1,  # 俯仰角
            roll=0.05,  # 滚转角
            snr=20.0,  # 信噪比
            confidence=0.9  # 置信度
        )
        signals.append(signal)
    
    # 创建航迹
    trajectory = TargetTrajectory(
        target_id="target_001",
        target_type="战斗机",
        signals=signals,
        tactical_mode="巡逻"
    )
    
    return trajectory


def main():
    """主函数"""
    logger.info("开始基本使用示例...")
    
    # 创建预测器
    predictor = TrajectoryPredictor()
    
    # 检查模型是否已训练
    if not predictor.is_ready():
        logger.warning("模型尚未训练，请先运行训练脚本")
        logger.info("可以运行: python scripts/train_model.py --use-sample-data")
        return
    
    # 创建示例航迹数据
    trajectory = create_sample_trajectory()
    
    logger.info(f"创建示例航迹，包含 {len(trajectory.signals)} 个数据点")
    logger.info(f"航迹持续时间: {trajectory.duration():.1f} 秒")
    
    # 进行预测
    logger.info("开始航迹预测...")
    prediction_result = predictor.predict_trajectory(trajectory)
    
    # 显示预测结果
    logger.info("=== 预测结果 ===")
    logger.info(f"目标ID: {prediction_result['target_id']}")
    logger.info(f"预测时间: {prediction_result['timestamp']}")
    
    if prediction_result.get('trajectory_prediction'):
        logger.info("航迹预测:")
        traj_pred = np.array(prediction_result['trajectory_prediction'])
        logger.info(f"  预测轨迹形状: {traj_pred.shape}")
        logger.info(f"  未来位置预测: {traj_pred[0]}")  # 显示第一个预测点
    
    if prediction_result.get('tactical_prediction'):
        tactical = prediction_result['tactical_prediction']
        logger.info("战术模式预测:")
        logger.info(f"  预测模式: {tactical['predicted_mode']}")
        logger.info(f"  置信度: {tactical['confidence']:.3f}")
        logger.info("  各模式概率:")
        for mode, prob in tactical['probabilities'].items():
            logger.info(f"    {mode}: {prob:.3f}")
    
    logger.info("示例运行完成！")


if __name__ == "__main__":
    main()
