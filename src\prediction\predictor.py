"""
航迹预测器主类
整合LSTM模型和战术分类器进行综合预测
"""

import numpy as np
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta

from src.models.lstm_model import LSTMTrajectoryModel
from src.models.classifier import TacticalClassifier
from src.data.radar_data import RadarSignal, TargetTrajectory
from config.model_config import lstm_config, classifier_config, prediction_config
from config.logging_config import default_logger as logger


class TrajectoryPredictor:
    """航迹预测器主类"""
    
    def __init__(self, 
                 lstm_model_path: Optional[str] = None,
                 classifier_model_path: Optional[str] = None):
        """
        初始化预测器
        
        Args:
            lstm_model_path: LSTM模型文件路径
            classifier_model_path: 分类器模型文件路径
        """
        self.lstm_model = LSTMTrajectoryModel(lstm_config)
        self.classifier = TacticalClassifier(classifier_config)
        
        # 加载预训练模型
        if lstm_model_path:
            self.lstm_model.load_model(lstm_model_path)
        
        if classifier_model_path:
            self.classifier.load_model(classifier_model_path)
        
        logger.info("航迹预测器初始化完成")
    
    def prepare_sequence_data(self, trajectory: TargetTrajectory) -> Optional[np.ndarray]:
        """
        准备LSTM输入的序列数据
        
        Args:
            trajectory: 目标航迹数据
            
        Returns:
            格式化的序列数据或None
        """
        if len(trajectory.signals) < lstm_config.sequence_length:
            logger.warning(f"航迹数据点不足，需要至少{lstm_config.sequence_length}个点")
            return None
        
        # 按时间排序
        sorted_signals = sorted(trajectory.signals, key=lambda x: x.timestamp)
        
        # 提取最近的序列数据
        recent_signals = sorted_signals[-lstm_config.sequence_length:]
        
        # 构建特征矩阵
        features = []
        for signal in recent_signals:
            feature_vector = [
                signal.position[0], signal.position[1], signal.position[2],
                signal.velocity[0], signal.velocity[1], signal.velocity[2]
            ]
            features.append(feature_vector)
        
        return np.array(features).reshape(1, lstm_config.sequence_length, lstm_config.input_dim)
    
    def extract_tactical_features(self, trajectory: TargetTrajectory) -> np.ndarray:
        """
        提取战术特征用于分类
        
        Args:
            trajectory: 目标航迹数据
            
        Returns:
            战术特征向量
        """
        if not trajectory.signals:
            return np.zeros(10)  # 返回默认特征向量
        
        signals = sorted(trajectory.signals, key=lambda x: x.timestamp)
        
        # 计算统计特征
        positions = np.array([s.position for s in signals])
        velocities = np.array([s.velocity for s in signals])
        
        # 位置特征
        pos_mean = np.mean(positions, axis=0)
        pos_std = np.std(positions, axis=0)
        
        # 速度特征
        vel_mean = np.mean(velocities, axis=0)
        vel_magnitude = np.mean([np.linalg.norm(v) for v in velocities])
        
        # 组合特征向量
        features = np.concatenate([
            pos_mean,      # 平均位置 (3维)
            pos_std,       # 位置标准差 (3维)
            vel_mean,      # 平均速度 (3维)
            [vel_magnitude] # 平均速度大小 (1维)
        ])
        
        return features
    
    def predict_trajectory(self, trajectory: TargetTrajectory) -> Dict:
        """
        预测目标航迹
        
        Args:
            trajectory: 目标航迹数据
            
        Returns:
            预测结果字典
        """
        result = {
            "target_id": trajectory.target_id,
            "timestamp": datetime.now(),
            "trajectory_prediction": None,
            "tactical_prediction": None,
            "confidence": 0.0
        }
        
        try:
            # 1. LSTM航迹预测
            sequence_data = self.prepare_sequence_data(trajectory)
            if sequence_data is not None and self.lstm_model.is_trained:
                trajectory_pred = self.lstm_model.predict(sequence_data)
                result["trajectory_prediction"] = trajectory_pred[0].tolist()
                logger.debug(f"航迹预测完成，目标: {trajectory.target_id}")
            
            # 2. 战术模式分类
            tactical_features = self.extract_tactical_features(trajectory)
            if self.classifier.is_trained:
                tactical_pred = self.classifier.predict_single(tactical_features)
                result["tactical_prediction"] = tactical_pred
                result["confidence"] = tactical_pred["confidence"]
                logger.debug(f"战术模式预测: {tactical_pred['predicted_mode']}")
            
        except Exception as e:
            logger.error(f"预测过程中发生错误: {str(e)}")
            result["error"] = str(e)
        
        return result
    
    def predict_multiple(self, trajectories: List[TargetTrajectory]) -> List[Dict]:
        """
        批量预测多个目标的航迹
        
        Args:
            trajectories: 目标航迹列表
            
        Returns:
            预测结果列表
        """
        results = []
        for trajectory in trajectories:
            result = self.predict_trajectory(trajectory)
            results.append(result)
        
        logger.info(f"完成{len(trajectories)}个目标的预测")
        return results
    
    def is_ready(self) -> bool:
        """检查预测器是否准备就绪"""
        return self.lstm_model.is_trained and self.classifier.is_trained
