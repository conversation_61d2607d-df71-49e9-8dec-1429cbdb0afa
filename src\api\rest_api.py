"""
REST API接口
提供航迹预测的HTTP API服务
"""

import sys
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Optional
import uvicorn

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

from src.prediction.real_time_engine import RealTimePredictionEngine
from src.data.radar_data import RadarSignal
from config.logging_config import setup_logging

logger = setup_logging()

# 创建FastAPI应用
app = FastAPI(
    title="航迹预测API",
    description="目标航迹ZZ跟踪实时预测模型API服务",
    version="0.1.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局预测引擎实例
prediction_engine: Optional[RealTimePredictionEngine] = None


class RadarSignalRequest(BaseModel):
    """雷达信号请求模型"""
    timestamp: str = Field(..., description="时间戳 (ISO格式)")
    station_id: str = Field(..., description="雷达站点ID")
    target_id: str = Field(..., description="目标ID")
    toa: float = Field(..., description="到达时间")
    pa: float = Field(..., description="相位角")
    position: List[float] = Field(..., description="位置坐标 [x, y, z]")
    velocity: List[float] = Field(..., description="速度向量 [vx, vy, vz]")
    heading: float = Field(0.0, description="航向角")
    elevation: float = Field(0.0, description="高度角")
    pitch: float = Field(0.0, description="俯仰角")
    roll: float = Field(0.0, description="滚转角")
    snr: float = Field(20.0, description="信噪比")
    confidence: float = Field(0.9, description="置信度")


class PredictionResponse(BaseModel):
    """预测结果响应模型"""
    target_id: str
    timestamp: str
    trajectory_prediction: Optional[List[List[float]]] = None
    tactical_prediction: Optional[Dict] = None
    data_points: int
    last_position: List[float]
    last_velocity: List[float]


@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    global prediction_engine
    
    logger.info("启动航迹预测API服务...")
    
    # 初始化预测引擎
    lstm_model_path = "data/models/lstm_trajectory_model.h5"
    classifier_model_path = "data/models/tactical_classifier_model.h5"
    
    try:
        prediction_engine = RealTimePredictionEngine(
            lstm_model_path=lstm_model_path,
            classifier_model_path=classifier_model_path,
            prediction_interval=1.0
        )
        prediction_engine.start()
        logger.info("预测引擎启动成功")
    except Exception as e:
        logger.error(f"预测引擎启动失败: {e}")
        prediction_engine = None


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    global prediction_engine
    
    if prediction_engine:
        prediction_engine.stop()
        logger.info("预测引擎已停止")


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "航迹预测API服务",
        "version": "0.1.0",
        "status": "运行中" if prediction_engine else "未初始化"
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "engine_status": "running" if prediction_engine and prediction_engine.is_running else "stopped"
    }


@app.post("/radar/signal", response_model=Dict)
async def add_radar_signal(signal_data: RadarSignalRequest):
    """添加雷达信号数据"""
    if not prediction_engine:
        raise HTTPException(status_code=503, detail="预测引擎未初始化")
    
    try:
        # 转换为RadarSignal对象
        signal = RadarSignal(
            timestamp=datetime.fromisoformat(signal_data.timestamp.replace('Z', '+00:00')),
            station_id=signal_data.station_id,
            target_id=signal_data.target_id,
            toa=signal_data.toa,
            pa=signal_data.pa,
            position=tuple(signal_data.position),
            velocity=tuple(signal_data.velocity),
            heading=signal_data.heading,
            elevation=signal_data.elevation,
            pitch=signal_data.pitch,
            roll=signal_data.roll,
            snr=signal_data.snr,
            confidence=signal_data.confidence
        )
        
        # 添加到预测引擎
        prediction_engine.add_radar_signal(signal)
        
        return {
            "status": "success",
            "message": f"雷达信号已添加，目标: {signal_data.target_id}",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"添加雷达信号失败: {e}")
        raise HTTPException(status_code=400, detail=f"处理雷达信号失败: {str(e)}")


@app.get("/prediction/{target_id}", response_model=PredictionResponse)
async def get_target_prediction(target_id: str):
    """获取指定目标的预测结果"""
    if not prediction_engine:
        raise HTTPException(status_code=503, detail="预测引擎未初始化")
    
    try:
        prediction = prediction_engine.predict_target(target_id)
        
        if prediction is None:
            raise HTTPException(status_code=404, detail=f"目标 {target_id} 未找到或数据不足")
        
        return PredictionResponse(
            target_id=prediction["target_id"],
            timestamp=prediction["timestamp"].isoformat(),
            trajectory_prediction=prediction.get("trajectory_prediction"),
            tactical_prediction=prediction.get("tactical_prediction"),
            data_points=prediction["data_points"],
            last_position=list(prediction["last_position"]),
            last_velocity=list(prediction["last_velocity"])
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取预测结果失败: {e}")
        raise HTTPException(status_code=500, detail=f"预测失败: {str(e)}")


@app.get("/predictions", response_model=Dict[str, PredictionResponse])
async def get_all_predictions():
    """获取所有目标的预测结果"""
    if not prediction_engine:
        raise HTTPException(status_code=503, detail="预测引擎未初始化")
    
    try:
        predictions = prediction_engine.predict_all_targets()
        
        response = {}
        for target_id, prediction in predictions.items():
            response[target_id] = PredictionResponse(
                target_id=prediction["target_id"],
                timestamp=prediction["timestamp"].isoformat(),
                trajectory_prediction=prediction.get("trajectory_prediction"),
                tactical_prediction=prediction.get("tactical_prediction"),
                data_points=prediction["data_points"],
                last_position=list(prediction["last_position"]),
                last_velocity=list(prediction["last_velocity"])
            )
        
        return response
        
    except Exception as e:
        logger.error(f"获取所有预测结果失败: {e}")
        raise HTTPException(status_code=500, detail=f"预测失败: {str(e)}")


@app.get("/targets/status")
async def get_targets_status():
    """获取所有目标的状态信息"""
    if not prediction_engine:
        raise HTTPException(status_code=503, detail="预测引擎未初始化")
    
    try:
        status = prediction_engine.get_target_status()
        
        # 转换datetime对象为字符串
        for target_id, info in status.items():
            if 'last_update' in info:
                info['last_update'] = info['last_update'].isoformat()
        
        return {
            "timestamp": datetime.now().isoformat(),
            "total_targets": len(status),
            "targets": status
        }
        
    except Exception as e:
        logger.error(f"获取目标状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")


def main():
    """启动API服务"""
    logger.info("启动航迹预测API服务...")
    
    uvicorn.run(
        "src.api.rest_api:app",
        host="0.0.0.0",
        port=8000,
        reload=False,
        log_level="info"
    )


if __name__ == "__main__":
    main()
