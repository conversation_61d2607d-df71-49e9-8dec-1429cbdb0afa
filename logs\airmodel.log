2025-09-03 04:19:23 | INFO     | src.prediction.real_time_engine:_load_models:62 - 加载LSTM预测模型...
2025-09-03 04:19:25 | INFO     | src.prediction.real_time_engine:_load_models:65 - 加载战术分类器...
2025-09-03 04:19:25 | INFO     | src.prediction.real_time_engine:_load_models:73 - 模型加载完成
2025-09-03 04:19:25 | INFO     | src.prediction.real_time_engine:start:249 - 启动实时预测引擎...
2025-09-03 04:19:25 | INFO     | src.prediction.real_time_engine:_prediction_loop:218 - 启动预测循环...
2025-09-03 04:19:25 | INFO     | src.prediction.real_time_engine:start:256 - 实时预测引擎已启动
2025-09-03 04:19:56 | INFO     | src.prediction.real_time_engine:stop:263 - 停止实时预测引擎...
2025-09-03 04:19:57 | INFO     | src.prediction.real_time_engine:stop:269 - 实时预测引擎已停止
2025-09-03 04:24:16 | INFO     | src.utils.visualization:plot_trajectory_3d:100 - 3D航迹图已保存: trajectory_3d_target_000.png
2025-09-03 04:24:17 | INFO     | src.utils.visualization:plot_trajectory_3d:100 - 3D航迹图已保存: trajectory_3d_target_001.png
2025-09-03 04:24:18 | INFO     | src.utils.visualization:plot_trajectory_3d:100 - 3D航迹图已保存: trajectory_3d_target_002.png
2025-09-03 04:24:20 | INFO     | src.utils.visualization:plot_multiple_trajectories:147 - 多目标航迹图已保存: multiple_trajectories.png
2025-09-03 04:24:20 | INFO     | src.utils.visualization:plot_tactical_distribution:238 - 战术模式分布图已保存: tactical_distribution.png
2025-09-03 04:24:23 | INFO     | src.utils.visualization:plot_prediction_accuracy:282 - 预测精度分析图已保存: prediction_accuracy.png
2025-09-03 04:28:11 | INFO     | src.data.data_loader:generate_sample_data:90 - 生成 3 个目标的示例数据...
2025-09-03 04:28:11 | INFO     | src.data.data_loader:generate_sample_data:149 - 生成完成，包含 3 个航迹
2025-09-03 04:28:11 | INFO     | src.data.data_loader:generate_sample_data:90 - 生成 2 个目标的示例数据...
2025-09-03 04:28:11 | INFO     | src.data.data_loader:generate_sample_data:149 - 生成完成，包含 2 个航迹
2025-09-03 04:28:11 | INFO     | src.data.preprocessor:prepare_training_data:165 - 准备训练数据，航迹数量: 2
2025-09-03 04:28:11 | INFO     | src.data.preprocessor:fit_scaler:99 - 拟合数据归一化器...
2025-09-03 04:28:11 | INFO     | src.data.preprocessor:fit_scaler:118 - 归一化器拟合完成，特征维度: 6
2025-09-03 04:28:11 | INFO     | src.data.preprocessor:prepare_training_data:196 - 训练数据准备完成，形状: X(2, 20, 6), y(2, 10, 3)
2025-09-03 04:44:25 | INFO     | __main__:train_enhanced_models:108 - 开始增强模型训练流程...
2025-09-03 04:44:25 | INFO     | __main__:train_enhanced_models:114 - 生成增强数据集...
2025-09-03 04:44:25 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:229 - 生成平衡数据集，每类100个样本...
2025-09-03 04:44:25 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:234 - 生成 巡逻任务 数据...
2025-09-03 04:44:27 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:234 - 生成 预警探测 数据...
2025-09-03 04:44:28 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:234 - 生成 电子侦察 数据...
2025-09-03 04:44:29 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:234 - 生成 电子干扰 数据...
2025-09-03 04:44:30 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:234 - 生成 对空攻击 数据...
2025-09-03 04:44:30 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:234 - 生成 对地攻击 数据...
2025-09-03 04:44:31 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:234 - 生成 空中格斗 数据...
2025-09-03 04:44:32 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:234 - 生成 撤退规避 数据...
2025-09-03 04:44:33 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:245 - 数据集生成完成，总计 800 个航迹
2025-09-03 04:44:33 | INFO     | __main__:train_enhanced_models:122 - 数据预处理...
2025-09-03 04:44:33 | INFO     | src.data.preprocessor:prepare_training_data:222 - 准备训练数据，航迹数量: 800
2025-09-03 04:44:33 | INFO     | src.data.preprocessor:fit_scaler:156 - 拟合数据归一化器...
2025-09-03 04:44:33 | INFO     | src.data.preprocessor:fit_scaler:175 - 归一化器拟合完成，特征维度: 27
2025-09-03 04:44:34 | INFO     | src.data.preprocessor:prepare_training_data:253 - 训练数据准备完成，形状: X(16800, 20, 27), y(16800, 10, 3)
2025-09-03 04:44:35 | INFO     | __main__:train_enhanced_models:143 - LSTM数据形状: X(16800, 20, 27), y(16800, 10, 3)
2025-09-03 04:44:35 | INFO     | __main__:train_enhanced_models:144 - 分类器数据形状: X(800, 15), 标签数量800
2025-09-03 04:44:35 | INFO     | __main__:train_enhanced_models:157 - 训练增强LSTM模型...
2025-09-03 04:44:35 | INFO     | __main__:create_enhanced_lstm_model:30 - 构建增强LSTM模型...
2025-09-03 04:44:40 | INFO     | __main__:create_enhanced_lstm_model:97 - 增强LSTM模型构建完成，参数量: 1665980
2025-09-03 04:46:20 | INFO     | __main__:train_enhanced_models:108 - 开始增强模型训练流程...
2025-09-03 04:46:20 | INFO     | __main__:train_enhanced_models:114 - 生成增强数据集...
2025-09-03 04:46:20 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:229 - 生成平衡数据集，每类50个样本...
2025-09-03 04:46:20 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:234 - 生成 巡逻任务 数据...
2025-09-03 04:46:21 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:234 - 生成 预警探测 数据...
2025-09-03 04:46:21 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:234 - 生成 电子侦察 数据...
2025-09-03 04:46:22 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:234 - 生成 电子干扰 数据...
2025-09-03 04:46:22 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:234 - 生成 对空攻击 数据...
2025-09-03 04:46:23 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:234 - 生成 对地攻击 数据...
2025-09-03 04:46:23 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:234 - 生成 空中格斗 数据...
2025-09-03 04:46:23 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:234 - 生成 撤退规避 数据...
2025-09-03 04:46:24 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:245 - 数据集生成完成，总计 400 个航迹
2025-09-03 04:46:24 | INFO     | __main__:train_enhanced_models:122 - 数据预处理...
2025-09-03 04:46:24 | INFO     | src.data.preprocessor:prepare_training_data:222 - 准备训练数据，航迹数量: 400
2025-09-03 04:46:24 | INFO     | src.data.preprocessor:fit_scaler:156 - 拟合数据归一化器...
2025-09-03 04:46:24 | INFO     | src.data.preprocessor:fit_scaler:175 - 归一化器拟合完成，特征维度: 27
2025-09-03 04:46:24 | INFO     | src.data.preprocessor:prepare_training_data:253 - 训练数据准备完成，形状: X(8400, 20, 27), y(8400, 10, 3)
2025-09-03 04:46:25 | INFO     | __main__:train_enhanced_models:143 - LSTM数据形状: X(8400, 20, 27), y(8400, 10, 3)
2025-09-03 04:46:25 | INFO     | __main__:train_enhanced_models:144 - 分类器数据形状: X(400, 15), 标签数量400
2025-09-03 04:46:25 | INFO     | __main__:train_enhanced_models:157 - 训练增强LSTM模型...
2025-09-03 04:46:25 | INFO     | __main__:create_enhanced_lstm_model:30 - 构建增强LSTM模型...
2025-09-03 04:46:29 | INFO     | __main__:create_enhanced_lstm_model:97 - 增强LSTM模型构建完成，参数量: 1665980
2025-09-03 04:48:09 | INFO     | __main__:train_enhanced_models:112 - 开始增强模型训练流程...
2025-09-03 04:48:09 | INFO     | __main__:train_enhanced_models:118 - 生成增强数据集...
2025-09-03 04:48:09 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:229 - 生成平衡数据集，每类30个样本...
2025-09-03 04:48:09 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:234 - 生成 巡逻任务 数据...
2025-09-03 04:48:09 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:234 - 生成 预警探测 数据...
2025-09-03 04:48:09 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:234 - 生成 电子侦察 数据...
2025-09-03 04:48:09 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:234 - 生成 电子干扰 数据...
2025-09-03 04:48:10 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:234 - 生成 对空攻击 数据...
2025-09-03 04:48:10 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:234 - 生成 对地攻击 数据...
2025-09-03 04:48:10 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:234 - 生成 空中格斗 数据...
2025-09-03 04:48:11 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:234 - 生成 撤退规避 数据...
2025-09-03 04:48:11 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:245 - 数据集生成完成，总计 240 个航迹
2025-09-03 04:48:11 | INFO     | __main__:train_enhanced_models:126 - 数据预处理...
2025-09-03 04:48:11 | INFO     | src.data.preprocessor:prepare_training_data:222 - 准备训练数据，航迹数量: 240
2025-09-03 04:48:11 | INFO     | src.data.preprocessor:fit_scaler:156 - 拟合数据归一化器...
2025-09-03 04:48:11 | INFO     | src.data.preprocessor:fit_scaler:175 - 归一化器拟合完成，特征维度: 27
2025-09-03 04:48:11 | INFO     | src.data.preprocessor:prepare_training_data:253 - 训练数据准备完成，形状: X(5040, 20, 27), y(5040, 10, 3)
2025-09-03 04:48:11 | INFO     | __main__:train_enhanced_models:147 - LSTM数据形状: X(5040, 20, 27), y(5040, 10, 3)
2025-09-03 04:48:11 | INFO     | __main__:train_enhanced_models:148 - 分类器数据形状: X(240, 15), 标签数量240
2025-09-03 04:48:11 | INFO     | __main__:train_enhanced_models:161 - 训练增强LSTM模型...
2025-09-03 04:48:11 | INFO     | __main__:create_enhanced_lstm_model:30 - 构建增强LSTM模型...
2025-09-03 04:48:16 | INFO     | __main__:create_enhanced_lstm_model:101 - 增强LSTM模型构建完成，参数量: 1683836
2025-09-03 05:00:57 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:229 - 生成平衡数据集，每类10个样本...
2025-09-03 05:00:57 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:234 - 生成 巡逻任务 数据...
2025-09-03 05:00:57 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:234 - 生成 预警探测 数据...
2025-09-03 05:00:57 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:234 - 生成 电子侦察 数据...
2025-09-03 05:00:57 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:234 - 生成 电子干扰 数据...
2025-09-03 05:00:57 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:234 - 生成 对空攻击 数据...
2025-09-03 05:00:57 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:234 - 生成 对地攻击 数据...
2025-09-03 05:00:57 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:234 - 生成 空中格斗 数据...
2025-09-03 05:00:57 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:234 - 生成 撤退规避 数据...
2025-09-03 05:00:57 | INFO     | src.data.enhanced_data_generator:generate_balanced_dataset:245 - 数据集生成完成，总计 80 个航迹
2025-09-03 05:00:59 | INFO     | src.evaluation.performance_evaluator:evaluate_trajectory_prediction:53 - 评估 Basic_LSTM 航迹预测性能...
2025-09-03 05:00:59 | INFO     | src.evaluation.performance_evaluator:evaluate_tactical_classification:119 - 评估 Enhanced_Classifier 战术分类性能...
2025-09-03 05:00:59 | INFO     | src.evaluation.performance_evaluator:evaluate_realtime_performance:177 - 评估实时性能...
2025-09-03 05:00:59 | INFO     | src.evaluation.performance_evaluator:evaluate_realtime_performance:200 - 实时性能评估完成:
2025-09-03 05:00:59 | INFO     | src.evaluation.performance_evaluator:evaluate_realtime_performance:201 -   - 平均预测延迟: 50.28ms
2025-09-03 05:00:59 | INFO     | src.evaluation.performance_evaluator:evaluate_realtime_performance:202 -   - P95延迟: 73.81ms
2025-09-03 05:00:59 | INFO     | src.evaluation.performance_evaluator:evaluate_realtime_performance:203 -   - 吞吐量: 1.67 pred/s
2025-09-03 05:00:59 | INFO     | src.evaluation.performance_evaluator:generate_comprehensive_report:209 - 生成综合评估报告...
2025-09-03 05:00:59 | INFO     | src.evaluation.performance_evaluator:generate_comprehensive_report:225 - 综合评估报告已保存: evaluation_results\comprehensive_evaluation_report.json
2025-09-03 05:00:59 | INFO     | src.evaluation.performance_evaluator:plot_performance_analysis:231 - 生成性能分析图表...
2025-09-07 05:07:31 | INFO     | src.data.data_loader:generate_sample_data:90 - 生成 3 个目标的示例数据...
2025-09-07 05:07:31 | INFO     | src.data.data_loader:generate_sample_data:90 - 生成 2 个目标的示例数据...
2025-09-07 05:07:31 | INFO     | src.models.lstm_model:build_model:33 - 构建增强LSTM模型架构...
2025-09-07 05:07:33 | INFO     | src.models.lstm_model:build_model:142 - 增强LSTM模型构建完成，参数量: 1667901
2025-09-07 05:07:33 | INFO     | src.models.lstm_model:build_model:33 - 构建增强LSTM模型架构...
2025-09-07 05:07:34 | INFO     | src.models.lstm_model:build_model:142 - 增强LSTM模型构建完成，参数量: 1667901
2025-09-07 05:07:34 | INFO     | src.models.lstm_model:train:163 - 开始训练LSTM模型...
2025-09-07 05:07:59 | INFO     | src.models.lstm_model:train:196 - LSTM模型训练完成
2025-09-07 05:07:59 | INFO     | src.models.lstm_model:build_model:33 - 构建增强LSTM模型架构...
2025-09-07 05:08:01 | INFO     | src.models.lstm_model:build_model:142 - 增强LSTM模型构建完成，参数量: 1667901
2025-09-07 05:08:01 | WARNING  | src.models.lstm_model:predict:214 - 模型可能未训练，预测结果可能不准确
2025-09-07 05:08:03 | INFO     | src.models.lstm_model:build_model:33 - 构建增强LSTM模型架构...
2025-09-07 05:08:05 | INFO     | src.models.lstm_model:build_model:142 - 增强LSTM模型构建完成，参数量: 1667901
2025-09-07 05:08:05 | INFO     | src.models.lstm_model:train:163 - 开始训练LSTM模型...
2025-09-07 05:08:25 | INFO     | src.models.lstm_model:train:196 - LSTM模型训练完成
2025-09-07 05:08:25 | INFO     | src.models.lstm_model:save_model:226 - 模型已保存到: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Jinn Alienware\pytest-0\test_model_save_load0\test_model.h5
2025-09-07 05:08:27 | INFO     | src.models.lstm_model:build_model:33 - 构建增强LSTM模型架构...
2025-09-07 05:08:29 | INFO     | src.models.lstm_model:build_model:142 - 增强LSTM模型构建完成，参数量: 1667901
2025-09-07 05:08:29 | INFO     | src.models.lstm_model:train:163 - 开始训练LSTM模型...
2025-09-07 05:08:49 | INFO     | src.models.lstm_model:train:196 - LSTM模型训练完成
2025-09-07 05:08:51 | INFO     | src.models.lstm_model:evaluate:248 - 模型评估结果: {'loss': 1.4374938011169434, 'mae': 0.8106862902641296, 'mse': 1.0259339809417725}
2025-09-07 05:08:51 | INFO     | src.models.lstm_model:build_model:33 - 构建增强LSTM模型架构...
2025-09-07 05:08:53 | INFO     | src.models.lstm_model:build_model:142 - 增强LSTM模型构建完成，参数量: 1667901
2025-09-07 05:26:40 | INFO     | src.models.lstm_model:build_model:33 - 构建增强LSTM模型架构...
2025-09-07 05:26:44 | INFO     | src.models.lstm_model:build_model:142 - 增强LSTM模型构建完成，参数量: 1667901
2025-09-07 05:26:44 | INFO     | src.models.lstm_model:train:163 - 开始训练LSTM模型...
2025-09-07 05:27:10 | INFO     | src.models.lstm_model:train:196 - LSTM模型训练完成
2025-09-07 05:27:10 | INFO     | src.models.lstm_model:save_model:226 - 模型已保存到: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Jinn Alienware\pytest-1\test_model_save_load0\test_model.h5
