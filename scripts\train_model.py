"""
模型训练脚本
用于训练LSTM预测模型和战术分类器
"""

import click
import numpy as np
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.models.lstm_model import LSTMTrajectoryModel
from config.model_config import lstm_config, classifier_config
from config.logging_config import setup_logging

logger = setup_logging()


def generate_sample_data():
    """生成示例训练数据"""
    logger.info("生成示例训练数据...")
    
    # 生成LSTM训练数据
    num_samples = 1000
    sequence_length = lstm_config.sequence_length
    input_dim = lstm_config.input_dim
    prediction_horizon = lstm_config.prediction_horizon
    output_dim = lstm_config.output_dim
    
    # 模拟航迹数据
    X_lstm = np.random.randn(num_samples, sequence_length, input_dim)
    y_lstm = np.random.randn(num_samples, prediction_horizon, output_dim)
    
    # 生成分类器训练数据
    X_classifier = np.random.randn(num_samples, 10)  # 10维战术特征
    y_classifier = np.random.choice(classifier_config.tactical_modes, num_samples)
    
    return X_lstm, y_lstm, X_classifier, y_classifier


@click.command()
@click.option('--data-path', default='data/processed', help='训练数据路径')
@click.option('--model-save-path', default='data/models', help='模型保存路径')
@click.option('--use-sample-data', is_flag=True, help='使用示例数据进行训练')
def train_models(data_path: str, model_save_path: str, use_sample_data: bool):
    """训练LSTM模型和战术分类器"""
    
    logger.info("开始模型训练流程...")
    
    # 创建保存目录
    Path(model_save_path).mkdir(parents=True, exist_ok=True)
    
    if use_sample_data:
        # 使用示例数据
        X_lstm, y_lstm, X_classifier, y_classifier = generate_sample_data()
    else:
        # TODO: 实现真实数据加载
        logger.error("真实数据加载功能尚未实现，请使用 --use-sample-data 选项")
        return
    
    # 数据分割
    split_idx = int(len(X_lstm) * 0.7)
    
    X_lstm_train, X_lstm_val = X_lstm[:split_idx], X_lstm[split_idx:]
    y_lstm_train, y_lstm_val = y_lstm[:split_idx], y_lstm[split_idx:]
    
    X_clf_train, X_clf_val = X_classifier[:split_idx], X_classifier[split_idx:]
    y_clf_train, y_clf_val = y_classifier[:split_idx], y_classifier[split_idx:]
    
    # 训练LSTM模型
    logger.info("训练LSTM航迹预测模型...")
    lstm_model = LSTMTrajectoryModel(lstm_config)
    lstm_model.build_model()
    
    lstm_history = lstm_model.train(
        X_lstm_train, y_lstm_train,
        X_lstm_val, y_lstm_val
    )
    
    # 保存LSTM模型
    lstm_model_path = Path(model_save_path) / "lstm_trajectory_model.h5"
    lstm_model.save_model(str(lstm_model_path))
    
    # 训练分类器（暂时跳过，专注于LSTM模型）
    logger.info("分类器训练暂时跳过，专注于LSTM模型训练...")
    
    logger.info("模型训练完成！")
    logger.info(f"LSTM模型保存在: {lstm_model_path}")
    logger.info(f"分类器保存在: {classifier_model_path}")


if __name__ == "__main__":
    train_models()
