"""
简化的模型训练脚本
直接训练LSTM模型，避免复杂的模块导入
"""

import sys
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers

# 设置日志
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_lstm_model(sequence_length=20, input_dim=6, prediction_horizon=10, output_dim=3):
    """创建LSTM模型"""
    logger.info("构建LSTM模型...")
    
    # 输入层
    inputs = keras.Input(shape=(sequence_length, input_dim), name="trajectory_input")
    
    # LSTM层
    x = layers.LSTM(128, return_sequences=True, dropout=0.2, name="lstm_1")(inputs)
    x = layers.LSTM(128, return_sequences=False, dropout=0.2, name="lstm_2")(x)
    
    # 全连接层
    x = layers.Dense(64, activation="relu", name="dense_1")(x)
    x = layers.Dropout(0.2)(x)
    
    # 输出层
    outputs = layers.Dense(output_dim * prediction_horizon, activation="linear", name="output")(x)
    outputs = layers.Reshape((prediction_horizon, output_dim), name="reshape_output")(outputs)
    
    # 创建模型
    model = keras.Model(inputs=inputs, outputs=outputs, name="LSTM_Trajectory_Model")
    
    # 编译模型
    model.compile(
        optimizer=keras.optimizers.Adam(learning_rate=0.001),
        loss="mse",
        metrics=["mae"]
    )
    
    logger.info(f"模型构建完成，参数量: {model.count_params()}")
    return model


def generate_sample_data(num_samples=1000, sequence_length=20, input_dim=6, 
                        prediction_horizon=10, output_dim=3):
    """生成示例训练数据"""
    logger.info("生成示例训练数据...")
    
    # 生成模拟的航迹数据
    X = np.random.randn(num_samples, sequence_length, input_dim)
    y = np.random.randn(num_samples, prediction_horizon, output_dim)
    
    # 添加一些趋势性，使数据更真实
    for i in range(num_samples):
        # 位置有连续性
        for t in range(1, sequence_length):
            X[i, t, :3] = X[i, t-1, :3] + X[i, t, 3:6] * 0.1  # 位置 = 前一位置 + 速度*时间
        
        # 预测目标也有连续性
        last_pos = X[i, -1, :3]
        last_vel = X[i, -1, 3:6]
        for t in range(prediction_horizon):
            y[i, t] = last_pos + last_vel * (t + 1) * 0.1
    
    return X, y


def main():
    """主训练函数"""
    logger.info("开始简化训练流程...")
    
    # 创建保存目录
    model_save_path = Path("data/models")
    model_save_path.mkdir(parents=True, exist_ok=True)
    
    # 生成示例数据
    X, y = generate_sample_data()
    
    # 数据分割
    split_idx = int(len(X) * 0.7)
    X_train, X_val = X[:split_idx], X[split_idx:]
    y_train, y_val = y[:split_idx], y[split_idx:]
    
    logger.info(f"训练数据: {X_train.shape}, 验证数据: {X_val.shape}")
    
    # 创建模型
    model = create_lstm_model()
    
    # 设置回调函数
    callbacks = [
        keras.callbacks.EarlyStopping(
            monitor="val_loss",
            patience=10,
            restore_best_weights=True
        ),
        keras.callbacks.ReduceLROnPlateau(
            monitor="val_loss",
            factor=0.5,
            patience=5,
            min_lr=1e-6
        )
    ]
    
    # 训练模型
    logger.info("开始训练模型...")
    history = model.fit(
        X_train, y_train,
        batch_size=32,
        epochs=20,  # 减少训练轮数用于快速测试
        validation_data=(X_val, y_val),
        callbacks=callbacks,
        verbose=1
    )
    
    # 保存模型
    model_path = model_save_path / "lstm_trajectory_model.h5"
    model.save(str(model_path))
    
    logger.info(f"模型训练完成并保存到: {model_path}")
    
    # 简单评估
    test_loss, test_mae = model.evaluate(X_val, y_val, verbose=0)
    logger.info(f"验证集性能 - Loss: {test_loss:.4f}, MAE: {test_mae:.4f}")


if __name__ == "__main__":
    main()
