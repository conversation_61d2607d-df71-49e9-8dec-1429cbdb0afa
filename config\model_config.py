"""
模型配置文件
定义LSTM模型、分类器等的超参数和配置
"""

from dataclasses import dataclass
from typing import List, Optional


@dataclass
class LSTMConfig:
    """LSTM模型配置（增强版）"""
    # 网络结构参数
    input_dim: int = 27  # 输入维度：实际特征向量维度
    hidden_dim: int = 256  # 增大隐藏层维度以处理更复杂特征
    num_layers: int = 3  # 增加LSTM层数
    output_dim: int = 6  # 输出维度：位置(x,y,z) + 速度(vx,vy,vz)
    dropout_rate: float = 0.3  # 增加Dropout率防止过拟合

    # 注意力机制参数
    use_attention: bool = True  # 启用注意力机制
    attention_dim: int = 64  # 注意力维度
    
    # 时间序列参数
    sequence_length: int = 20  # 输入序列长度
    prediction_horizon: int = 10  # 预测时间步长
    
    # 训练参数
    batch_size: int = 32
    learning_rate: float = 0.001
    epochs: int = 100
    validation_split: float = 0.3
    early_stopping_patience: int = 10
    
    # 优化器配置
    optimizer: str = "adam"  # 使用Adam优化器
    loss_function: str = "mse"  # 均方误差损失


@dataclass
class ClassifierConfig:
    """战术模式分类器配置（增强版）"""
    # 分类类别（扩展到8类）
    tactical_modes: List[str] = None
    num_classes: int = 8  # 8类战术任务

    # 网络参数（增强）
    hidden_layers: List[int] = None  # [128, 64, 32]
    dropout_rate: float = 0.4  # 增加dropout防止过拟合
    activation: str = "relu"
    use_batch_norm: bool = True  # 启用批归一化

    # 训练参数
    batch_size: int = 128  # 增大批大小
    learning_rate: float = 0.0005  # 降低学习率提高稳定性
    epochs: int = 100  # 增加训练轮数

    # 高级训练策略
    use_class_weights: bool = True  # 使用类别权重处理不平衡数据
    use_focal_loss: bool = True     # 使用Focal Loss
    label_smoothing: float = 0.1    # 标签平滑

    def __post_init__(self):
        if self.tactical_modes is None:
            self.tactical_modes = [
                "巡逻任务", "预警探测", "电子侦察", "电子干扰",
                "对空攻击", "对地攻击", "空中格斗", "撤退规避"
            ]
        if self.hidden_layers is None:
            self.hidden_layers = [128, 64, 32]


@dataclass
class DataConfig:
    """数据处理配置"""
    # 雷达站点配置
    max_stations: int = 10  # 最大雷达站点数
    min_stations: int = 2   # 最小雷达站点数
    
    # 数据预处理
    normalization_method: str = "minmax"  # 归一化方法
    outlier_threshold: float = 3.0  # 异常值检测阈值
    
    # 时间窗口
    time_window: float = 60.0  # 时间窗口（秒）
    sampling_rate: float = 1.0  # 采样率（Hz）
    
    # 数据路径
    raw_data_path: str = "data/raw"
    processed_data_path: str = "data/processed"
    model_save_path: str = "data/models"


@dataclass
class PredictionConfig:
    """预测配置"""
    # 实时预测
    prediction_interval: float = 1.0  # 预测间隔（秒）
    max_prediction_time: float = 300.0  # 最大预测时间（秒）
    
    # 置信度阈值
    confidence_threshold: float = 0.8
    
    # 缓存配置
    cache_size: int = 1000  # 缓存大小
    cache_ttl: int = 300  # 缓存生存时间（秒）


# 全局配置实例
lstm_config = LSTMConfig()
classifier_config = ClassifierConfig()
data_config = DataConfig()
prediction_config = PredictionConfig()
