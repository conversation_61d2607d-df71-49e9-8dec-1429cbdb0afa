# 深度学习框架
tensorflow>=2.12.0
keras>=2.12.0

# 数据处理
numpy>=1.24.0
pandas>=2.0.0
scipy>=1.10.0

# 可视化
matplotlib>=3.7.0
plotly>=5.14.0
seaborn>=0.12.0

# 实时处理
fastapi>=0.100.0
uvicorn>=0.22.0
websockets>=11.0

# 数据存储
redis>=4.5.0
h5py>=3.8.0

# 配置和工具
pydantic>=2.0.0
python-dotenv>=1.0.0
loguru>=0.7.0
click>=8.1.0

# 测试
pytest>=7.3.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0

# 开发工具
black>=23.0.0
flake8>=6.0.0
mypy>=1.3.0
