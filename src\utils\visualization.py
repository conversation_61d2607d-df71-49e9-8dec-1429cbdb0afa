"""
航迹可视化工具
提供2D和3D航迹可视化功能
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import plotly.graph_objects as go
import plotly.express as px
from typing import List, Dict, Optional, Tuple
from datetime import datetime

from src.data.radar_data import TargetTrajectory, RadarSignal, RadarStation
from config.logging_config import default_logger as logger


class TrajectoryVisualizer:
    """航迹可视化器"""
    
    def __init__(self, figsize: Tuple[int, int] = (12, 8)):
        """
        初始化可视化器
        
        Args:
            figsize: 图形大小
        """
        self.figsize = figsize
        self.colors = ['blue', 'red', 'green', 'orange', 'purple', 'brown', 'pink', 'gray']
        self.tactical_colors = {
            '巡逻': 'blue',
            '预警探测': 'green', 
            '攻击突击': 'red',
            '空中格斗': 'orange',
            '对抗': 'purple',
            '其他': 'gray'
        }
    
    def plot_trajectory_3d(self, trajectory: TargetTrajectory, 
                          prediction: Optional[np.ndarray] = None,
                          save_path: Optional[str] = None) -> plt.Figure:
        """
        绘制3D航迹图
        
        Args:
            trajectory: 目标航迹
            prediction: 预测轨迹数据
            save_path: 保存路径
            
        Returns:
            matplotlib图形对象
        """
        fig = plt.figure(figsize=self.figsize)
        ax = fig.add_subplot(111, projection='3d')
        
        # 提取历史轨迹
        positions = trajectory.get_positions()
        
        if len(positions) == 0:
            logger.warning("航迹数据为空")
            return fig
        
        # 绘制历史轨迹
        color = self.tactical_colors.get(trajectory.tactical_mode, 'blue')
        ax.plot(positions[:, 0], positions[:, 1], positions[:, 2], 
                color=color, marker='o', linewidth=2, markersize=4,
                label=f'历史轨迹 ({trajectory.tactical_mode})')
        
        # 绘制预测轨迹
        if prediction is not None:
            # 连接历史和预测轨迹
            last_pos = positions[-1]
            first_pred = prediction[0]
            ax.plot([last_pos[0], first_pred[0]], 
                   [last_pos[1], first_pred[1]], 
                   [last_pos[2], first_pred[2]], 
                   'g--', linewidth=1, alpha=0.7)
            
            # 绘制预测轨迹
            ax.plot(prediction[:, 0], prediction[:, 1], prediction[:, 2], 
                    'r-s', linewidth=2, markersize=6, label='预测轨迹')
        
        # 标记起点和终点
        ax.scatter(*positions[0], color='green', s=100, marker='^', label='起点')
        ax.scatter(*positions[-1], color='black', s=100, marker='v', label='当前位置')
        
        if prediction is not None:
            ax.scatter(*prediction[-1], color='red', s=100, marker='*', label='预测终点')
        
        # 设置标签和标题
        ax.set_xlabel('X 坐标 (m)')
        ax.set_ylabel('Y 坐标 (m)')
        ax.set_zlabel('Z 坐标 (m)')
        ax.set_title(f'目标 {trajectory.target_id} 航迹预测')
        ax.legend()
        
        # 保存图片
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"3D航迹图已保存: {save_path}")
        
        return fig
    
    def plot_multiple_trajectories(self, trajectories: List[TargetTrajectory],
                                 predictions: Optional[Dict[str, np.ndarray]] = None,
                                 save_path: Optional[str] = None) -> plt.Figure:
        """
        绘制多个目标的航迹
        
        Args:
            trajectories: 航迹列表
            predictions: 预测结果字典
            save_path: 保存路径
            
        Returns:
            matplotlib图形对象
        """
        fig = plt.figure(figsize=(15, 10))
        ax = fig.add_subplot(111, projection='3d')
        
        for i, trajectory in enumerate(trajectories):
            positions = trajectory.get_positions()
            if len(positions) == 0:
                continue
            
            color = self.colors[i % len(self.colors)]
            
            # 绘制历史轨迹
            ax.plot(positions[:, 0], positions[:, 1], positions[:, 2], 
                    color=color, marker='o', linewidth=2, markersize=3,
                    label=f'{trajectory.target_id} ({trajectory.tactical_mode})')
            
            # 绘制预测轨迹
            if predictions and trajectory.target_id in predictions:
                pred = predictions[trajectory.target_id]
                ax.plot(pred[:, 0], pred[:, 1], pred[:, 2], 
                        color=color, linestyle='--', linewidth=2, alpha=0.7)
        
        ax.set_xlabel('X 坐标 (m)')
        ax.set_ylabel('Y 坐标 (m)')
        ax.set_zlabel('Z 坐标 (m)')
        ax.set_title('多目标航迹预测')
        ax.legend()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"多目标航迹图已保存: {save_path}")
        
        return fig
    
    def plot_interactive_3d(self, trajectory: TargetTrajectory,
                           prediction: Optional[np.ndarray] = None) -> go.Figure:
        """
        创建交互式3D航迹图
        
        Args:
            trajectory: 目标航迹
            prediction: 预测轨迹
            
        Returns:
            Plotly图形对象
        """
        fig = go.Figure()
        
        positions = trajectory.get_positions()
        if len(positions) == 0:
            return fig
        
        # 历史轨迹
        fig.add_trace(go.Scatter3d(
            x=positions[:, 0],
            y=positions[:, 1], 
            z=positions[:, 2],
            mode='lines+markers',
            name=f'历史轨迹 ({trajectory.tactical_mode})',
            line=dict(color='blue', width=4),
            marker=dict(size=4)
        ))
        
        # 预测轨迹
        if prediction is not None:
            fig.add_trace(go.Scatter3d(
                x=prediction[:, 0],
                y=prediction[:, 1],
                z=prediction[:, 2],
                mode='lines+markers',
                name='预测轨迹',
                line=dict(color='red', width=4, dash='dash'),
                marker=dict(size=6, symbol='square')
            ))
        
        # 设置布局
        fig.update_layout(
            title=f'目标 {trajectory.target_id} 交互式航迹预测',
            scene=dict(
                xaxis_title='X 坐标 (m)',
                yaxis_title='Y 坐标 (m)',
                zaxis_title='Z 坐标 (m)'
            ),
            width=800,
            height=600
        )
        
        return fig
    
    def plot_tactical_distribution(self, trajectories: List[TargetTrajectory],
                                 save_path: Optional[str] = None) -> plt.Figure:
        """
        绘制战术模式分布图
        
        Args:
            trajectories: 航迹列表
            save_path: 保存路径
            
        Returns:
            matplotlib图形对象
        """
        # 统计战术模式
        tactical_counts = {}
        for trajectory in trajectories:
            mode = trajectory.tactical_mode or '未知'
            tactical_counts[mode] = tactical_counts.get(mode, 0) + 1
        
        # 创建饼图
        fig, ax = plt.subplots(figsize=self.figsize)
        
        modes = list(tactical_counts.keys())
        counts = list(tactical_counts.values())
        colors = [self.tactical_colors.get(mode, 'gray') for mode in modes]
        
        wedges, texts, autotexts = ax.pie(counts, labels=modes, colors=colors, 
                                         autopct='%1.1f%%', startangle=90)
        
        ax.set_title('战术模式分布')
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"战术模式分布图已保存: {save_path}")
        
        return fig
    
    def plot_prediction_accuracy(self, actual_positions: np.ndarray,
                               predicted_positions: np.ndarray,
                               save_path: Optional[str] = None) -> plt.Figure:
        """
        绘制预测精度分析图
        
        Args:
            actual_positions: 实际位置
            predicted_positions: 预测位置
            save_path: 保存路径
            
        Returns:
            matplotlib图形对象
        """
        # 计算预测误差
        errors = np.linalg.norm(actual_positions - predicted_positions, axis=1)
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 误差随时间变化
        ax1.plot(errors, 'b-o', linewidth=2, markersize=4)
        ax1.set_xlabel('预测时间步')
        ax1.set_ylabel('预测误差 (m)')
        ax1.set_title('预测误差随时间变化')
        ax1.grid(True, alpha=0.3)
        
        # 误差分布直方图
        ax2.hist(errors, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        ax2.set_xlabel('预测误差 (m)')
        ax2.set_ylabel('频次')
        ax2.set_title('预测误差分布')
        ax2.axvline(np.mean(errors), color='red', linestyle='--', 
                   label=f'平均误差: {np.mean(errors):.2f}m')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"预测精度分析图已保存: {save_path}")
        
        return fig
