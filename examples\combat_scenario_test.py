"""
实战场景测试
模拟复杂的空战场景，测试模型在实战环境下的性能
"""

import sys
import numpy as np
import time
from pathlib import Path
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from typing import List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.radar_data import RadarSignal, TargetTrajectory
import tensorflow as tf

# 设置日志
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CombatScenarioSimulator:
    """实战场景模拟器"""
    
    def __init__(self):
        self.scenarios = {
            "多目标空战": self.simulate_multi_target_combat,
            "电子对抗": self.simulate_electronic_warfare,
            "突防攻击": self.simulate_penetration_attack,
            "防空作战": self.simulate_air_defense
        }
    
    def simulate_multi_target_combat(self) -> List[TargetTrajectory]:
        """模拟多目标空战场景"""
        logger.info("模拟多目标空战场景...")
        
        trajectories = []
        base_time = datetime.now()
        
        # 场景：4架战斗机进行空中格斗
        targets = [
            {"id": "红方_001", "mode": "空中格斗", "pos": [2000, 3000, 8000], "vel": [300, 200, 50]},
            {"id": "红方_002", "mode": "空中格斗", "pos": [2500, 3500, 8200], "vel": [280, 180, 30]},
            {"id": "蓝方_001", "mode": "空中格斗", "pos": [8000, 7000, 8500], "vel": [-350, -250, 20]},
            {"id": "蓝方_002", "mode": "空中格斗", "pos": [8500, 7500, 8300], "vel": [-320, -220, 10]}
        ]
        
        for target in targets:
            signals = []
            current_pos = np.array(target["pos"], dtype=float)
            current_vel = np.array(target["vel"], dtype=float)
            
            for t in range(60):  # 60秒的格斗
                # 高机动飞行
                if t % 10 == 0:  # 每10秒执行大机动
                    maneuver = np.random.uniform(-100, 100, 3)
                    current_vel += maneuver
                
                # 位置更新
                current_pos += current_vel * 1.0
                
                # 计算加速度
                if t > 0:
                    acceleration = (current_vel - prev_vel) / 1.0
                else:
                    acceleration = np.array([0.0, 0.0, 0.0])
                
                # 格斗中的剧烈姿态变化
                pitch = np.random.uniform(-0.8, 0.8)
                roll = np.random.uniform(-1.2, 1.2)
                yaw = np.random.uniform(-0.5, 0.5)
                
                # 电子对抗影响
                comm_status = np.random.choice([0, 1, 2], p=[0.7, 0.2, 0.1])
                signal_strength = np.random.uniform(0.4, 0.9)
                
                signal = RadarSignal(
                    timestamp=base_time + timedelta(seconds=t),
                    station_id="combat_radar",
                    target_id=target["id"],
                    toa=0.1 + t * 0.01,
                    pa=np.pi/4 + t * 0.02,
                    position=tuple(current_pos + np.random.normal(0, 20, 3)),
                    position_accuracy=(15.0, 15.0, 25.0),
                    velocity=tuple(current_vel + np.random.normal(0, 10, 3)),
                    acceleration=tuple(acceleration),
                    heading=np.arctan2(current_vel[1], current_vel[0]),
                    elevation=np.arctan2(current_vel[2], np.sqrt(current_vel[0]**2 + current_vel[1]**2)),
                    pitch=pitch,
                    roll=roll,
                    yaw=yaw,
                    target_distance=np.linalg.norm(current_pos),
                    pulse_width=np.random.uniform(0.8, 1.5),
                    radar_frequency=np.random.uniform(2900, 3100),
                    prf=np.random.uniform(2000, 4000),
                    signal_strength=signal_strength,
                    communication_status=comm_status,
                    snr=np.random.uniform(12, 22),
                    confidence=np.random.uniform(0.7, 0.9)
                )
                
                signals.append(signal)
                prev_vel = current_vel.copy()
            
            trajectory = TargetTrajectory(
                target_id=target["id"],
                target_type="战斗机",
                signals=signals,
                tactical_mode=target["mode"]
            )
            trajectories.append(trajectory)
        
        return trajectories
    
    def simulate_electronic_warfare(self) -> List[TargetTrajectory]:
        """模拟电子对抗场景"""
        logger.info("模拟电子对抗场景...")
        
        trajectories = []
        base_time = datetime.now()
        
        # 场景：电子侦察机 + 电子干扰机 + 攻击机
        targets = [
            {"id": "电侦_001", "mode": "电子侦察", "pos": [10000, 5000, 12000], "vel": [180, 100, 5]},
            {"id": "干扰_001", "mode": "电子干扰", "pos": [8000, 6000, 11000], "vel": [50, 30, 0]},
            {"id": "攻击_001", "mode": "对空攻击", "pos": [15000, 2000, 9000], "vel": [-400, 200, 50]}
        ]
        
        for target in targets:
            signals = []
            current_pos = np.array(target["pos"], dtype=float)
            current_vel = np.array(target["vel"], dtype=float)
            
            for t in range(45):
                # 根据任务类型调整行为
                if target["mode"] == "电子侦察":
                    # 侦察：复杂航线，频繁转向
                    if t % 8 == 0:
                        turn_angle = np.random.uniform(-np.pi/3, np.pi/3)
                        speed = np.linalg.norm(current_vel)
                        current_vel = np.array([
                            speed * np.cos(turn_angle),
                            speed * np.sin(turn_angle),
                            current_vel[2]
                        ])
                
                elif target["mode"] == "电子干扰":
                    # 干扰：相对固定位置，小幅机动
                    current_vel += np.random.normal(0, 5, 3)
                
                elif target["mode"] == "对空攻击":
                    # 攻击：高速直线接近
                    current_vel += np.random.normal(0, 20, 3)
                
                current_pos += current_vel * 1.0
                
                # 电子对抗环境的信号特征
                if target["mode"] in ["电子侦察", "电子干扰"]:
                    comm_status = np.random.choice([0, 1, 2], p=[0.4, 0.4, 0.2])
                    signal_strength = np.random.uniform(0.3, 0.7)
                    snr = np.random.uniform(10, 18)
                else:
                    comm_status = np.random.choice([0, 1], p=[0.8, 0.2])
                    signal_strength = np.random.uniform(0.6, 0.9)
                    snr = np.random.uniform(15, 23)
                
                signal = RadarSignal(
                    timestamp=base_time + timedelta(seconds=t),
                    station_id="ew_radar",
                    target_id=target["id"],
                    toa=0.1 + t * 0.01,
                    pa=np.pi/4 + t * 0.02,
                    position=tuple(current_pos + np.random.normal(0, 15, 3)),
                    position_accuracy=(12.0, 12.0, 20.0),
                    velocity=tuple(current_vel + np.random.normal(0, 8, 3)),
                    acceleration=(0.0, 0.0, 0.0),
                    heading=np.arctan2(current_vel[1], current_vel[0]),
                    elevation=0.1,
                    pitch=np.random.uniform(-0.3, 0.3),
                    roll=np.random.uniform(-0.2, 0.2),
                    yaw=np.random.uniform(-0.1, 0.1),
                    target_distance=np.linalg.norm(current_pos),
                    pulse_width=np.random.uniform(0.5, 2.0),
                    radar_frequency=np.random.uniform(2800, 3200),
                    prf=np.random.uniform(1500, 3500),
                    signal_strength=signal_strength,
                    communication_status=comm_status,
                    snr=snr,
                    confidence=np.random.uniform(0.6, 0.9)
                )
                
                signals.append(signal)
            
            trajectory = TargetTrajectory(
                target_id=target["id"],
                target_type="特种作战飞机",
                signals=signals,
                tactical_mode=target["mode"]
            )
            trajectories.append(trajectory)
        
        return trajectories
    
    def simulate_penetration_attack(self) -> List[TargetTrajectory]:
        """模拟突防攻击场景"""
        logger.info("模拟突防攻击场景...")
        
        trajectories = []
        base_time = datetime.now()
        
        # 场景：低空突防 + 高空掩护
        targets = [
            {"id": "突防_001", "mode": "对地攻击", "pos": [20000, 1000, 500], "vel": [-450, 100, -10]},
            {"id": "掩护_001", "mode": "对空攻击", "pos": [18000, 3000, 8000], "vel": [-300, 150, 0]},
            {"id": "预警_001", "mode": "预警探测", "pos": [5000, 8000, 15000], "vel": [100, -50, 10]}
        ]
        
        for target in targets:
            signals = []
            current_pos = np.array(target["pos"], dtype=float)
            current_vel = np.array(target["vel"], dtype=float)
            
            for t in range(40):
                # 突防特征：低空高速
                if target["mode"] == "对地攻击":
                    # 保持低空
                    if current_pos[2] < 200:
                        current_vel[2] = max(current_vel[2], 0)
                    
                    # 地形跟随机动
                    if t % 5 == 0:
                        current_vel += np.random.normal(0, 30, 3)
                
                current_pos += current_vel * 1.0
                
                # 低空突防的雷达特征
                if target["mode"] == "对地攻击":
                    # 地杂波影响
                    snr = np.random.uniform(8, 15)
                    signal_strength = np.random.uniform(0.4, 0.7)
                else:
                    snr = np.random.uniform(15, 25)
                    signal_strength = np.random.uniform(0.7, 0.9)
                
                signal = RadarSignal(
                    timestamp=base_time + timedelta(seconds=t),
                    station_id="defense_radar",
                    target_id=target["id"],
                    toa=0.1 + t * 0.01,
                    pa=np.pi/4 + t * 0.02,
                    position=tuple(current_pos + np.random.normal(0, 25, 3)),
                    position_accuracy=(20.0, 20.0, 30.0),
                    velocity=tuple(current_vel + np.random.normal(0, 15, 3)),
                    acceleration=(0.0, 0.0, 0.0),
                    heading=np.arctan2(current_vel[1], current_vel[0]),
                    elevation=np.arctan2(current_vel[2], np.sqrt(current_vel[0]**2 + current_vel[1]**2)),
                    pitch=np.random.uniform(-0.4, 0.4),
                    roll=np.random.uniform(-0.3, 0.3),
                    yaw=np.random.uniform(-0.2, 0.2),
                    target_distance=np.linalg.norm(current_pos),
                    pulse_width=np.random.uniform(0.8, 1.8),
                    radar_frequency=np.random.uniform(2850, 3150),
                    prf=np.random.uniform(1800, 3800),
                    signal_strength=signal_strength,
                    communication_status=0,
                    snr=snr,
                    confidence=np.random.uniform(0.6, 0.85)
                )
                
                signals.append(signal)
            
            trajectory = TargetTrajectory(
                target_id=target["id"],
                target_type="攻击机",
                signals=signals,
                tactical_mode=target["mode"]
            )
            trajectories.append(trajectory)
        
        return trajectories
    
    def simulate_air_defense(self) -> List[TargetTrajectory]:
        """模拟防空作战场景"""
        logger.info("模拟防空作战场景...")
        
        trajectories = []
        base_time = datetime.now()
        
        # 场景：来袭目标 + 拦截导弹
        targets = [
            {"id": "来袭_001", "mode": "对地攻击", "pos": [25000, 2000, 3000], "vel": [-500, 50, -30]},
            {"id": "拦截_001", "mode": "对空攻击", "pos": [5000, 3000, 1000], "vel": [400, -20, 100]}
        ]
        
        for target in targets:
            signals = []
            current_pos = np.array(target["pos"], dtype=float)
            current_vel = np.array(target["vel"], dtype=float)
            
            for t in range(35):
                current_pos += current_vel * 1.0
                
                # 拦截机动
                if target["mode"] == "对空攻击" and t > 15:
                    # 拦截阶段：调整航向指向目标
                    target_pos = np.array([25000 - 500*t, 2000 + 50*t, 3000 - 30*t])
                    direction = target_pos - current_pos
                    direction_norm = direction / np.linalg.norm(direction)
                    current_vel = direction_norm * np.linalg.norm(current_vel)
                
                signal = RadarSignal(
                    timestamp=base_time + timedelta(seconds=t),
                    station_id="defense_system",
                    target_id=target["id"],
                    toa=0.1 + t * 0.01,
                    pa=np.pi/4 + t * 0.02,
                    position=tuple(current_pos + np.random.normal(0, 12, 3)),
                    position_accuracy=(10.0, 10.0, 18.0),
                    velocity=tuple(current_vel + np.random.normal(0, 8, 3)),
                    acceleration=(0.0, 0.0, 0.0),
                    heading=np.arctan2(current_vel[1], current_vel[0]),
                    elevation=np.arctan2(current_vel[2], np.sqrt(current_vel[0]**2 + current_vel[1]**2)),
                    pitch=np.random.uniform(-0.3, 0.3),
                    roll=np.random.uniform(-0.2, 0.2),
                    yaw=np.random.uniform(-0.15, 0.15),
                    target_distance=np.linalg.norm(current_pos),
                    pulse_width=np.random.uniform(0.6, 1.2),
                    radar_frequency=np.random.uniform(2900, 3100),
                    prf=np.random.uniform(2000, 4000),
                    signal_strength=np.random.uniform(0.7, 0.95),
                    communication_status=0,
                    snr=np.random.uniform(18, 25),
                    confidence=np.random.uniform(0.8, 0.95)
                )
                
                signals.append(signal)
            
            trajectory = TargetTrajectory(
                target_id=target["id"],
                target_type="攻击机" if "来袭" in target["id"] else "拦截机",
                signals=signals,
                tactical_mode=target["mode"]
            )
            trajectories.append(trajectory)
        
        return trajectories


def test_scenario_prediction(scenario_name: str, trajectories: List[TargetTrajectory]):
    """测试场景预测性能"""
    logger.info(f"测试 {scenario_name} 预测性能...")
    
    # 加载模型
    lstm_model_path = "data/models/lstm_trajectory_model.h5"
    classifier_model_path = "data/models/enhanced_tactical_classifier.h5"
    
    if not Path(lstm_model_path).exists():
        logger.error("LSTM模型不存在")
        return
    
    if not Path(classifier_model_path).exists():
        logger.error("分类器模型不存在")
        return
    
    try:
        lstm_model = tf.keras.models.load_model(lstm_model_path)
        classifier_model = tf.keras.models.load_model(classifier_model_path)
        
        # 加载分类器标签
        labels_path = classifier_model_path.replace('.h5', '.labels.npy')
        if Path(labels_path).exists():
            class_labels = np.load(labels_path, allow_pickle=True).item()
            reverse_labels = {v: k for k, v in class_labels.items()}
        else:
            reverse_labels = {i: f"类别_{i}" for i in range(8)}
        
        scenario_results = []
        
        for trajectory in trajectories:
            if len(trajectory.signals) < 20:
                continue
            
            # 准备LSTM输入（基础6维特征）
            recent_signals = trajectory.signals[-20:]
            lstm_input = np.zeros((1, 20, 6))
            
            for i, signal in enumerate(recent_signals):
                lstm_input[0, i, :] = [
                    signal.position[0], signal.position[1], signal.position[2],
                    signal.velocity[0], signal.velocity[1], signal.velocity[2]
                ]
            
            # LSTM预测
            start_time = time.time()
            traj_pred = lstm_model.predict(lstm_input, verbose=0)
            lstm_time = (time.time() - start_time) * 1000
            
            # 准备分类器输入（15维战术特征）
            tactical_features = extract_tactical_features_simple(trajectory.signals)
            tactical_input = tactical_features.reshape(1, -1)
            
            # 分类预测
            start_time = time.time()
            class_pred = classifier_model.predict(tactical_input, verbose=0)
            classifier_time = (time.time() - start_time) * 1000
            
            predicted_class = np.argmax(class_pred[0])
            confidence = float(class_pred[0][predicted_class])
            predicted_mode = reverse_labels.get(predicted_class, "未知")
            
            result = {
                "target_id": trajectory.target_id,
                "true_mode": trajectory.tactical_mode,
                "predicted_mode": predicted_mode,
                "confidence": confidence,
                "lstm_time_ms": lstm_time,
                "classifier_time_ms": classifier_time,
                "total_time_ms": lstm_time + classifier_time,
                "trajectory_prediction": traj_pred[0].tolist()
            }
            
            scenario_results.append(result)
            
            logger.info(f"  {trajectory.target_id}: {trajectory.tactical_mode} -> {predicted_mode} "
                       f"(置信度:{confidence:.3f}, 总时间:{lstm_time + classifier_time:.1f}ms)")
        
        # 场景统计
        correct_predictions = sum(1 for r in scenario_results if r["true_mode"] == r["predicted_mode"])
        total_predictions = len(scenario_results)
        accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
        
        avg_time = np.mean([r["total_time_ms"] for r in scenario_results])
        avg_confidence = np.mean([r["confidence"] for r in scenario_results])
        
        logger.info(f"{scenario_name} 场景结果:")
        logger.info(f"  - 预测准确率: {accuracy:.3f} ({correct_predictions}/{total_predictions})")
        logger.info(f"  - 平均预测时间: {avg_time:.2f}ms")
        logger.info(f"  - 平均置信度: {avg_confidence:.3f}")
        
        return scenario_results
        
    except Exception as e:
        logger.error(f"场景测试失败: {e}")
        return []


def extract_tactical_features_simple(signals: List[RadarSignal]) -> np.ndarray:
    """简化的战术特征提取"""
    if len(signals) < 5:
        return np.zeros(15)
    
    # 计算基本统计特征
    positions = np.array([s.position for s in signals])
    velocities = np.array([s.velocity for s in signals])
    
    # 运动特征
    speeds = [np.linalg.norm(v) for v in velocities]
    avg_speed = np.mean(speeds)
    speed_std = np.std(speeds)
    max_speed = np.max(speeds)
    
    # 高度特征
    altitudes = positions[:, 2]
    avg_altitude = np.mean(altitudes)
    altitude_change = np.max(altitudes) - np.min(altitudes)
    altitude_trend = (altitudes[-1] - altitudes[0]) / len(altitudes)
    
    # 信号特征
    avg_signal_strength = np.mean([s.signal_strength for s in signals])
    avg_snr = np.mean([s.snr for s in signals])
    comm_disruption = np.mean([s.communication_status for s in signals])
    
    # 其他特征
    features = [
        avg_speed, speed_std, max_speed, 0, 0, 0,  # 运动特征（6维）
        avg_altitude, altitude_change, altitude_trend,  # 高度特征（3维）
        0, 0, 0,  # 姿态特征（3维）
        avg_signal_strength, comm_disruption, avg_snr  # 信号特征（3维）
    ]
    
    return np.array(features)


def main():
    """主测试函数"""
    logger.info("开始实战场景测试...")
    
    simulator = CombatScenarioSimulator()
    
    # 测试各种场景
    all_results = {}
    
    for scenario_name, simulate_func in simulator.scenarios.items():
        logger.info(f"\n=== {scenario_name} 场景测试 ===")
        
        # 生成场景数据
        trajectories = simulate_func()
        logger.info(f"生成了 {len(trajectories)} 个目标航迹")
        
        # 测试预测性能
        results = test_scenario_prediction(scenario_name, trajectories)
        all_results[scenario_name] = results
    
    # 综合分析
    logger.info("\n=== 实战场景综合分析 ===")
    
    total_correct = 0
    total_predictions = 0
    total_times = []
    
    for scenario_name, results in all_results.items():
        if results:
            correct = sum(1 for r in results if r["true_mode"] == r["predicted_mode"])
            total = len(results)
            accuracy = correct / total if total > 0 else 0
            avg_time = np.mean([r["total_time_ms"] for r in results])
            
            total_correct += correct
            total_predictions += total
            total_times.extend([r["total_time_ms"] for r in results])
            
            logger.info(f"{scenario_name}: 准确率 {accuracy:.3f}, 平均时间 {avg_time:.1f}ms")
    
    # 总体性能
    overall_accuracy = total_correct / total_predictions if total_predictions > 0 else 0
    overall_avg_time = np.mean(total_times) if total_times else 0
    
    logger.info(f"\n总体性能:")
    logger.info(f"  - 整体准确率: {overall_accuracy:.3f}")
    logger.info(f"  - 平均预测时间: {overall_avg_time:.2f}ms")
    logger.info(f"  - 总预测次数: {total_predictions}")
    
    logger.info("实战场景测试完成！")


if __name__ == "__main__":
    main()
