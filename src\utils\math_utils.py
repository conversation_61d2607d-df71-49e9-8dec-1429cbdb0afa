"""
数学计算工具
提供航迹预测相关的数学计算函数
"""

import numpy as np
from typing import Tuple, List
import math


class MathUtils:
    """数学工具类"""
    
    @staticmethod
    def calculate_distance_3d(pos1: <PERSON><PERSON>[float, float, float], 
                             pos2: <PERSON><PERSON>[float, float, float]) -> float:
        """计算3D空间中两点间的距离"""
        dx = pos1[0] - pos2[0]
        dy = pos1[1] - pos2[1]
        dz = pos1[2] - pos2[2]
        return math.sqrt(dx**2 + dy**2 + dz**2)
    
    @staticmethod
    def calculate_velocity(pos1: <PERSON>ple[float, float, float],
                          pos2: <PERSON>ple[float, float, float],
                          time_delta: float) -> <PERSON>ple[float, float, float]:
        """计算速度向量"""
        if time_delta == 0:
            return (0.0, 0.0, 0.0)
        
        vx = (pos2[0] - pos1[0]) / time_delta
        vy = (pos2[1] - pos1[1]) / time_delta
        vz = (pos2[2] - pos1[2]) / time_delta
        
        return (vx, vy, vz)
    
    @staticmethod
    def calculate_acceleration(vel1: Tuple[float, float, float],
                             vel2: Tuple[float, float, float],
                             time_delta: float) -> Tuple[float, float, float]:
        """计算加速度向量"""
        if time_delta == 0:
            return (0.0, 0.0, 0.0)
        
        ax = (vel2[0] - vel1[0]) / time_delta
        ay = (vel2[1] - vel1[1]) / time_delta
        az = (vel2[2] - vel1[2]) / time_delta
        
        return (ax, ay, az)
    
    @staticmethod
    def calculate_heading(velocity: Tuple[float, float, float]) -> float:
        """计算航向角（弧度）"""
        vx, vy, _ = velocity
        return math.atan2(vy, vx)
    
    @staticmethod
    def calculate_elevation_angle(velocity: Tuple[float, float, float]) -> float:
        """计算高度角（弧度）"""
        vx, vy, vz = velocity
        horizontal_speed = math.sqrt(vx**2 + vy**2)
        if horizontal_speed == 0:
            return 0.0
        return math.atan2(vz, horizontal_speed)
    
    @staticmethod
    def smooth_trajectory(positions: np.ndarray, window_size: int = 5) -> np.ndarray:
        """平滑航迹数据"""
        if len(positions) < window_size:
            return positions
        
        smoothed = np.zeros_like(positions)
        half_window = window_size // 2
        
        for i in range(len(positions)):
            start_idx = max(0, i - half_window)
            end_idx = min(len(positions), i + half_window + 1)
            smoothed[i] = np.mean(positions[start_idx:end_idx], axis=0)
        
        return smoothed
    
    @staticmethod
    def interpolate_trajectory(positions: np.ndarray, 
                             timestamps: List[float],
                             target_timestamps: List[float]) -> np.ndarray:
        """插值航迹数据"""
        interpolated = np.zeros((len(target_timestamps), positions.shape[1]))
        
        for dim in range(positions.shape[1]):
            interpolated[:, dim] = np.interp(target_timestamps, timestamps, positions[:, dim])
        
        return interpolated
    
    @staticmethod
    def calculate_trajectory_curvature(positions: np.ndarray) -> np.ndarray:
        """计算航迹曲率"""
        if len(positions) < 3:
            return np.zeros(len(positions))
        
        curvatures = np.zeros(len(positions))
        
        for i in range(1, len(positions) - 1):
            # 使用三点计算曲率
            p1, p2, p3 = positions[i-1], positions[i], positions[i+1]
            
            # 计算向量
            v1 = p2 - p1
            v2 = p3 - p2
            
            # 计算曲率
            cross_product = np.cross(v1, v2)
            if isinstance(cross_product, np.ndarray):
                cross_magnitude = np.linalg.norm(cross_product)
            else:
                cross_magnitude = abs(cross_product)
            
            v1_magnitude = np.linalg.norm(v1)
            
            if v1_magnitude > 0:
                curvatures[i] = cross_magnitude / (v1_magnitude ** 3)
        
        return curvatures
