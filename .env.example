# 环境配置示例文件
# 复制此文件为 .env 并修改相应配置

# 数据库配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# 模型配置
MODEL_PATH=data/models
LSTM_MODEL_FILE=lstm_trajectory_model.h5
CLASSIFIER_MODEL_FILE=tactical_classifier_model.h5

# API配置
API_HOST=0.0.0.0
API_PORT=8000
WEBSOCKET_PORT=8001

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/airmodel.log

# 雷达站点配置
MAX_RADAR_STATIONS=10
MIN_RADAR_STATIONS=2

# 预测配置
PREDICTION_INTERVAL=1.0
MAX_PREDICTION_TIME=300.0
CONFIDENCE_THRESHOLD=0.8

# GPU配置
USE_GPU=true
GPU_MEMORY_LIMIT=4096
