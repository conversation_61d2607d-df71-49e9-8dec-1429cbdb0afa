"""
多站点雷达数据融合模块
实现多侦查平台协同观测和TOA/PA数据时序对齐
"""

import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from collections import defaultdict
import logging

from .radar_data import RadarSignal, RadarStation, TargetTrajectory

logger = logging.getLogger(__name__)


@dataclass
class MultiStationSignal:
    """多站点融合信号"""
    timestamp: datetime
    target_id: str
    station_signals: Dict[str, RadarSignal]  # 站点ID -> 信号
    fused_toa: float
    fused_pa: float
    fusion_confidence: float


class MultiStationDataFusion:
    """多站点数据融合器"""
    
    def __init__(self, time_window: float = 0.1, min_stations: int = 2):
        """
        初始化多站点数据融合器
        
        Args:
            time_window: 时间窗口（秒），用于信号对齐
            min_stations: 最少站点数量要求
        """
        self.time_window = time_window
        self.min_stations = min_stations
        self.stations: Dict[str, RadarStation] = {}
        self.signal_buffer: Dict[str, List[RadarSignal]] = defaultdict(list)
        
    def add_station(self, station: RadarStation):
        """添加雷达站点"""
        self.stations[station.station_id] = station
        logger.info(f"添加雷达站点: {station.name} ({station.station_id})")
    
    def add_signal(self, signal: RadarSignal):
        """添加雷达信号到缓冲区"""
        self.signal_buffer[signal.target_id].append(signal)
    
    def align_signals_by_time(self, signals: List[RadarSignal]) -> List[List[RadarSignal]]:
        """
        按时间窗口对齐多站点信号
        
        Args:
            signals: 信号列表
            
        Returns:
            时间对齐的信号组列表
        """
        if not signals:
            return []
        
        # 按时间排序
        sorted_signals = sorted(signals, key=lambda x: x.timestamp)
        
        aligned_groups = []
        current_group = [sorted_signals[0]]
        base_time = sorted_signals[0].timestamp
        
        for signal in sorted_signals[1:]:
            time_diff = (signal.timestamp - base_time).total_seconds()
            
            if time_diff <= self.time_window:
                # 在时间窗口内，加入当前组
                current_group.append(signal)
            else:
                # 超出时间窗口，开始新组
                if len(current_group) >= self.min_stations:
                    aligned_groups.append(current_group)
                
                current_group = [signal]
                base_time = signal.timestamp
        
        # 添加最后一组
        if len(current_group) >= self.min_stations:
            aligned_groups.append(current_group)
        
        return aligned_groups
    
    def fuse_toa_pa_data(self, station_signals: List[RadarSignal]) -> Tuple[float, float, float]:
        """
        融合多站点的TOA和PA数据
        
        Args:
            station_signals: 同一时间窗口内的多站点信号
            
        Returns:
            (融合TOA, 融合PA, 融合置信度)
        """
        if not station_signals:
            return 0.0, 0.0, 0.0
        
        # 提取TOA和PA数据
        toa_values = [signal.toa for signal in station_signals]
        pa_values = [signal.pa for signal in station_signals]
        confidences = [signal.confidence for signal in station_signals]
        
        # 加权融合（基于置信度）
        total_confidence = sum(confidences)
        if total_confidence == 0:
            # 简单平均
            fused_toa = np.mean(toa_values)
            fused_pa = np.mean(pa_values)
            fusion_confidence = 0.5
        else:
            # 置信度加权平均
            weights = [conf / total_confidence for conf in confidences]
            fused_toa = sum(toa * w for toa, w in zip(toa_values, weights))
            fused_pa = sum(pa * w for pa, w in zip(pa_values, weights))
            fusion_confidence = min(1.0, total_confidence / len(station_signals))
        
        return fused_toa, fused_pa, fusion_confidence
    
    def create_fused_trajectory(self, target_id: str) -> Optional[TargetTrajectory]:
        """
        为指定目标创建融合后的航迹
        
        Args:
            target_id: 目标ID
            
        Returns:
            融合后的航迹数据
        """
        if target_id not in self.signal_buffer:
            return None
        
        signals = self.signal_buffer[target_id]
        if len(signals) < self.min_stations:
            logger.warning(f"目标 {target_id} 的信号数量不足，需要至少 {self.min_stations} 个站点")
            return None
        
        # 时间对齐
        aligned_groups = self.align_signals_by_time(signals)
        
        if not aligned_groups:
            logger.warning(f"目标 {target_id} 没有足够的时间对齐信号组")
            return None
        
        # 创建融合信号
        fused_signals = []
        
        for group in aligned_groups:
            # 按站点分组
            station_signals_dict = {}
            for signal in group:
                station_signals_dict[signal.station_id] = signal
            
            # 融合TOA/PA数据
            fused_toa, fused_pa, fusion_conf = self.fuse_toa_pa_data(group)
            
            # 选择主信号（置信度最高的）
            main_signal = max(group, key=lambda x: x.confidence)
            
            # 创建融合信号
            fused_signal = RadarSignal(
                timestamp=main_signal.timestamp,
                station_id="FUSED",
                target_id=target_id,
                toa=fused_toa,
                pa=fused_pa,
                position=main_signal.position,
                position_accuracy=main_signal.position_accuracy,
                velocity=main_signal.velocity,
                acceleration=main_signal.acceleration,
                heading=main_signal.heading,
                elevation=main_signal.elevation,
                pitch=main_signal.pitch,
                roll=main_signal.roll,
                yaw=main_signal.yaw,
                target_distance=main_signal.target_distance,
                pulse_width=main_signal.pulse_width,
                radar_frequency=main_signal.radar_frequency,
                prf=main_signal.prf,
                signal_strength=main_signal.signal_strength,
                communication_status=main_signal.communication_status,
                snr=main_signal.snr,
                confidence=fusion_conf
            )
            
            fused_signals.append(fused_signal)
        
        # 创建融合航迹
        fused_trajectory = TargetTrajectory(
            target_id=target_id,
            target_type="unknown",
            signals=fused_signals,
            tactical_mode=None
        )
        
        logger.info(f"目标 {target_id} 融合完成，融合信号数: {len(fused_signals)}")
        return fused_trajectory
    
    def create_time_series_samples(self, trajectory: TargetTrajectory, 
                                 sample_length: int = 20) -> List[np.ndarray]:
        """
        创建时序学习样本
        
        Args:
            trajectory: 融合后的航迹
            sample_length: 样本长度
            
        Returns:
            时序样本列表
        """
        if len(trajectory.signals) < sample_length:
            return []
        
        samples = []
        
        # 滑动窗口创建样本
        for i in range(len(trajectory.signals) - sample_length + 1):
            sample_signals = trajectory.signals[i:i + sample_length]
            
            # 提取TOA和PA时序
            toa_sequence = [signal.toa for signal in sample_signals]
            pa_sequence = [signal.pa for signal in sample_signals]
            
            # 组合为样本
            sample = np.array([toa_sequence, pa_sequence]).T  # (sample_length, 2)
            samples.append(sample)
        
        return samples


class AWACSDetectionModeClassifier:
    """预警机探测模式分类器"""
    
    def __init__(self):
        self.detection_modes = [
            "机械扫描",  # 传统机械扫描模式
            "扇面扫描",  # 扇面扫描模式  
            "引导拦截"   # 引导拦截模式
        ]
        self.mode_features = {
            "机械扫描": {
                "scan_pattern": "circular",
                "scan_speed": "constant",
                "beam_width": "wide",
                "dwell_time": "short"
            },
            "扇面扫描": {
                "scan_pattern": "sector",
                "scan_speed": "variable", 
                "beam_width": "medium",
                "dwell_time": "medium"
            },
            "引导拦截": {
                "scan_pattern": "track",
                "scan_speed": "adaptive",
                "beam_width": "narrow", 
                "dwell_time": "long"
            }
        }
    
    def extract_detection_features(self, toa_pa_samples: List[np.ndarray]) -> np.ndarray:
        """
        从TOA/PA时序样本中提取探测模式特征
        
        Args:
            toa_pa_samples: TOA/PA时序样本列表
            
        Returns:
            探测模式特征向量
        """
        if not toa_pa_samples:
            return np.zeros(12)
        
        # 合并所有样本
        all_toa = []
        all_pa = []
        
        for sample in toa_pa_samples:
            all_toa.extend(sample[:, 0])
            all_pa.extend(sample[:, 1])
        
        toa_array = np.array(all_toa)
        pa_array = np.array(all_pa)
        
        # 提取特征
        features = [
            # TOA特征 (6维)
            np.mean(toa_array),           # TOA均值
            np.std(toa_array),            # TOA标准差
            np.max(toa_array) - np.min(toa_array),  # TOA范围
            len(np.where(np.diff(toa_array) > 0.01)[0]),  # TOA跳变次数
            np.mean(np.abs(np.diff(toa_array))),  # TOA变化率
            np.var(np.diff(toa_array)),   # TOA变化方差
            
            # PA特征 (6维)
            np.mean(pa_array),            # PA均值
            np.std(pa_array),             # PA标准差
            np.max(pa_array) - np.min(pa_array),  # PA范围
            len(np.where(np.abs(np.diff(pa_array)) > 0.1)[0]),  # PA跳变次数
            np.mean(np.abs(np.diff(pa_array))),  # PA变化率
            np.var(np.diff(pa_array))     # PA变化方差
        ]
        
        return np.array(features)
    
    def classify_detection_mode(self, features: np.ndarray) -> Dict[str, float]:
        """
        基于特征分类探测模式
        
        Args:
            features: 探测模式特征向量
            
        Returns:
            各模式的概率分布
        """
        # 简化的规则分类器（实际应用中可用机器学习模型）
        
        toa_std = features[1]
        pa_std = features[7]
        toa_changes = features[3]
        pa_changes = features[9]
        
        # 机械扫描：TOA和PA都相对稳定，变化规律
        mechanical_score = 1.0 / (1.0 + toa_std + pa_std)
        
        # 扇面扫描：PA变化较大，TOA相对稳定
        sector_score = pa_std / (1.0 + toa_std)
        
        # 引导拦截：TOA和PA都有较大变化，跟踪特定目标
        intercept_score = (toa_changes + pa_changes) / 20.0
        
        # 归一化
        total_score = mechanical_score + sector_score + intercept_score
        if total_score == 0:
            total_score = 1.0
        
        return {
            "机械扫描": mechanical_score / total_score,
            "扇面扫描": sector_score / total_score,
            "引导拦截": intercept_score / total_score
        }


class AWACSTrajectoryPredictor:
    """预警机状态预测器"""
    
    def __init__(self, fusion_engine: MultiStationDataFusion):
        self.fusion_engine = fusion_engine
        self.mode_classifier = AWACSDetectionModeClassifier()
        self.lstm_model = None
        
    def load_lstm_model(self, model_path: str):
        """加载LSTM预测模型"""
        try:
            import tensorflow as tf
            self.lstm_model = tf.keras.models.load_model(model_path)
            logger.info(f"LSTM模型加载成功: {model_path}")
        except Exception as e:
            logger.error(f"LSTM模型加载失败: {e}")
    
    def predict_awacs_state(self, target_id: str, sequence_length: int = 20) -> Dict:
        """
        预测预警机状态
        
        Args:
            target_id: 预警机目标ID
            sequence_length: 序列长度
            
        Returns:
            预测结果字典
        """
        # 1. 获取融合航迹
        fused_trajectory = self.fusion_engine.create_fused_trajectory(target_id)
        if not fused_trajectory:
            return {"error": "无法创建融合航迹"}
        
        # 2. 创建时序样本
        toa_pa_samples = self.fusion_engine.create_time_series_samples(
            fused_trajectory, sequence_length
        )
        
        if not toa_pa_samples:
            return {"error": "时序样本不足"}
        
        # 3. 探测模式分类
        detection_features = self.mode_classifier.extract_detection_features(toa_pa_samples)
        mode_probabilities = self.mode_classifier.classify_detection_mode(detection_features)
        
        # 4. LSTM轨迹预测
        trajectory_prediction = None
        if self.lstm_model and len(fused_trajectory.signals) >= sequence_length:
            try:
                # 准备LSTM输入
                recent_signals = fused_trajectory.signals[-sequence_length:]
                lstm_input = np.zeros((1, sequence_length, 2))  # TOA, PA
                
                for i, signal in enumerate(recent_signals):
                    lstm_input[0, i, 0] = signal.toa
                    lstm_input[0, i, 1] = signal.pa
                
                # 预测
                prediction = self.lstm_model.predict(lstm_input, verbose=0)
                trajectory_prediction = prediction[0].tolist()
                
            except Exception as e:
                logger.error(f"LSTM预测失败: {e}")
        
        return {
            "target_id": target_id,
            "detection_mode_probabilities": mode_probabilities,
            "predicted_mode": max(mode_probabilities.items(), key=lambda x: x[1])[0],
            "trajectory_prediction": trajectory_prediction,
            "fusion_confidence": np.mean([s.confidence for s in fused_trajectory.signals]),
            "station_count": len(set(s.station_id for s in fused_trajectory.signals)),
            "sample_count": len(toa_pa_samples)
        }


def create_multi_station_demo():
    """创建多站点演示场景"""
    
    # 1. 创建多个雷达站点
    stations = [
        RadarStation(
            station_id="radar_001",
            name="东部站点",
            position=(0.0, 0.0, 100.0),
            detection_range=200.0,
            frequency=3000.0
        ),
        RadarStation(
            station_id="radar_002", 
            name="西部站点",
            position=(150.0, 50.0, 120.0),
            detection_range=180.0,
            frequency=3200.0
        ),
        RadarStation(
            station_id="radar_003",
            name="南部站点", 
            position=(75.0, -100.0, 110.0),
            detection_range=190.0,
            frequency=2800.0
        )
    ]
    
    # 2. 创建融合引擎
    fusion_engine = MultiStationDataFusion(time_window=0.2, min_stations=2)
    
    for station in stations:
        fusion_engine.add_station(station)
    
    # 3. 模拟预警机信号
    awacs_signals = []
    base_time = datetime.now()
    
    # 模拟机械扫描模式的预警机
    for t in range(30):
        for station in stations:
            # 模拟不同站点接收到的信号
            time_offset = np.random.uniform(-0.05, 0.05)  # 时间同步误差
            
            signal = RadarSignal(
                timestamp=base_time + timedelta(seconds=t + time_offset),
                station_id=station.station_id,
                target_id="awacs_001",
                toa=0.1 + t * 0.005 + np.random.normal(0, 0.001),  # 机械扫描的TOA模式
                pa=np.pi/3 + t * 0.01 + np.random.normal(0, 0.005),  # 机械扫描的PA模式
                position=(5000 + t * 100, 8000 + t * 50, 12000),
                position_accuracy=(10.0, 10.0, 20.0),
                velocity=(100, 50, 0),
                acceleration=(0, 0, 0),
                heading=np.pi/4,
                elevation=0.1,
                pitch=0.05,
                roll=0.02,
                yaw=0.01,
                target_distance=np.linalg.norm([5000 + t * 100, 8000 + t * 50, 12000]),
                pulse_width=1.0,
                radar_frequency=station.frequency,
                prf=2000.0,
                signal_strength=0.8,
                communication_status=0,
                snr=20.0 + np.random.normal(0, 2),
                confidence=0.9 + np.random.normal(0, 0.05)
            )
            
            fusion_engine.add_signal(signal)
    
    return fusion_engine


if __name__ == "__main__":
    # 演示多站点数据融合
    logger.info("=== 多站点数据融合演示 ===")
    
    fusion_engine = create_multi_station_demo()
    predictor = AWACSTrajectoryPredictor(fusion_engine)
    
    # 预测预警机状态
    result = predictor.predict_awacs_state("awacs_001")
    
    print("预警机状态预测结果:")
    print(f"  目标ID: {result.get('target_id')}")
    print(f"  预测探测模式: {result.get('predicted_mode')}")
    print(f"  模式概率分布: {result.get('detection_mode_probabilities')}")
    print(f"  融合置信度: {result.get('fusion_confidence', 0):.3f}")
    print(f"  参与站点数: {result.get('station_count')}")
    print(f"  时序样本数: {result.get('sample_count')}")
