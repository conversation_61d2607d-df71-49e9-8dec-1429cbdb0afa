"""
实时预测引擎
处理实时数据流并提供航迹预测服务
"""

import asyncio
import numpy as np
from typing import Dict, List, Optional, Callable
from datetime import datetime, timedelta
from collections import deque
import threading
import time

from src.data.radar_data import RadarSignal, TargetTrajectory, RadarDataManager
from config.logging_config import default_logger as logger


class RealTimePredictionEngine:
    """实时预测引擎"""
    
    def __init__(self, 
                 lstm_model_path: str,
                 classifier_model_path: str,
                 prediction_interval: float = 1.0,
                 max_trajectory_length: int = 100):
        """
        初始化实时预测引擎
        
        Args:
            lstm_model_path: LSTM模型路径
            classifier_model_path: 分类器模型路径
            prediction_interval: 预测间隔（秒）
            max_trajectory_length: 最大航迹长度
        """
        self.lstm_model_path = lstm_model_path
        self.classifier_model_path = classifier_model_path
        self.prediction_interval = prediction_interval
        self.max_trajectory_length = max_trajectory_length
        
        # 数据管理
        self.data_manager = RadarDataManager()
        self.target_buffers: Dict[str, deque] = {}  # 每个目标的数据缓冲区
        
        # 预测结果缓存
        self.prediction_cache: Dict[str, Dict] = {}
        
        # 回调函数
        self.prediction_callbacks: List[Callable] = []
        
        # 运行状态
        self.is_running = False
        self.prediction_thread: Optional[threading.Thread] = None
        
        # 加载模型
        self._load_models()
    
    def _load_models(self):
        """加载预训练模型"""
        try:
            import tensorflow as tf
            
            logger.info("加载LSTM预测模型...")
            self.lstm_model = tf.keras.models.load_model(self.lstm_model_path)
            
            logger.info("加载战术分类器...")
            self.classifier_model = tf.keras.models.load_model(self.classifier_model_path)
            
            # 加载分类器标签
            labels_path = self.classifier_model_path.replace('.h5', '.labels.npy')
            self.class_labels = np.load(labels_path, allow_pickle=True).item()
            self.reverse_labels = {v: k for k, v in self.class_labels.items()}
            
            logger.info("模型加载完成")
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            self.lstm_model = None
            self.classifier_model = None
    
    def add_radar_signal(self, signal: RadarSignal):
        """添加新的雷达信号"""
        target_id = signal.target_id
        
        # 初始化目标缓冲区
        if target_id not in self.target_buffers:
            self.target_buffers[target_id] = deque(maxlen=self.max_trajectory_length)
        
        # 添加信号到缓冲区
        self.target_buffers[target_id].append(signal)
        
        # 更新数据管理器
        self.data_manager.add_signal(signal)
        
        logger.debug(f"添加雷达信号，目标: {target_id}, 缓冲区大小: {len(self.target_buffers[target_id])}")
    
    def extract_tactical_features(self, signals: List[RadarSignal]) -> np.ndarray:
        """提取战术特征"""
        if len(signals) < 5:  # 需要足够的数据点
            return np.zeros(10)
        
        # 计算统计特征
        positions = np.array([s.position for s in signals])
        velocities = np.array([s.velocity for s in signals])
        
        # 速度统计
        speed_values = [np.linalg.norm(v) for v in velocities]
        avg_speed = np.mean(speed_values)
        speed_std = np.std(speed_values)
        
        # 高度统计
        altitudes = positions[:, 2]
        avg_altitude = np.mean(altitudes)
        altitude_std = np.std(altitudes)
        
        # 航向变化率
        headings = [s.heading for s in signals]
        heading_changes = np.diff(headings)
        avg_heading_change = np.mean(np.abs(heading_changes))
        
        # 姿态变化
        pitches = [s.pitch for s in signals]
        rolls = [s.roll for s in signals]
        avg_pitch_change = np.mean(np.abs(np.diff(pitches)))
        avg_roll_change = np.mean(np.abs(np.diff(rolls)))
        
        # 信号质量
        avg_snr = np.mean([s.snr for s in signals])
        avg_confidence = np.mean([s.confidence for s in signals])
        
        # 航迹持续时间
        duration = (signals[-1].timestamp - signals[0].timestamp).total_seconds()
        
        return np.array([
            avg_speed, speed_std, avg_altitude, altitude_std,
            avg_heading_change, avg_pitch_change, avg_roll_change,
            avg_snr, avg_confidence, duration
        ])
    
    def predict_target(self, target_id: str) -> Optional[Dict]:
        """预测单个目标的航迹和战术模式"""
        if target_id not in self.target_buffers:
            return None
        
        signals = list(self.target_buffers[target_id])
        if len(signals) < 20:  # 需要足够的历史数据
            return None
        
        try:
            # 准备LSTM输入数据
            recent_signals = signals[-20:]  # 最近20个数据点
            lstm_input = np.zeros((1, 20, 6))
            
            for i, signal in enumerate(recent_signals):
                lstm_input[0, i, :] = [
                    signal.position[0], signal.position[1], signal.position[2],
                    signal.velocity[0], signal.velocity[1], signal.velocity[2]
                ]
            
            # LSTM航迹预测
            trajectory_pred = None
            if self.lstm_model is not None:
                trajectory_pred = self.lstm_model.predict(lstm_input, verbose=0)
            
            # 战术模式分类
            tactical_pred = None
            if self.classifier_model is not None:
                tactical_features = self.extract_tactical_features(signals)
                tactical_features = tactical_features.reshape(1, -1)
                class_probs = self.classifier_model.predict(tactical_features, verbose=0)
                
                predicted_class = np.argmax(class_probs[0])
                confidence = float(class_probs[0][predicted_class])
                
                tactical_pred = {
                    "predicted_mode": self.reverse_labels[predicted_class],
                    "confidence": confidence,
                    "probabilities": {
                        self.reverse_labels[i]: float(prob) 
                        for i, prob in enumerate(class_probs[0])
                    }
                }
            
            # 构建预测结果
            result = {
                "target_id": target_id,
                "timestamp": datetime.now(),
                "trajectory_prediction": trajectory_pred[0].tolist() if trajectory_pred is not None else None,
                "tactical_prediction": tactical_pred,
                "data_points": len(signals),
                "last_position": signals[-1].position,
                "last_velocity": signals[-1].velocity
            }
            
            return result
            
        except Exception as e:
            logger.error(f"预测目标 {target_id} 时发生错误: {e}")
            return None
    
    def predict_all_targets(self) -> Dict[str, Dict]:
        """预测所有目标"""
        results = {}
        
        for target_id in self.target_buffers.keys():
            prediction = self.predict_target(target_id)
            if prediction is not None:
                results[target_id] = prediction
                self.prediction_cache[target_id] = prediction
        
        return results
    
    def add_prediction_callback(self, callback: Callable[[Dict], None]):
        """添加预测结果回调函数"""
        self.prediction_callbacks.append(callback)
    
    def _prediction_loop(self):
        """预测循环（在单独线程中运行）"""
        logger.info("启动预测循环...")
        
        while self.is_running:
            try:
                # 执行预测
                predictions = self.predict_all_targets()
                
                # 调用回调函数
                for callback in self.prediction_callbacks:
                    try:
                        callback(predictions)
                    except Exception as e:
                        logger.error(f"回调函数执行失败: {e}")
                
                # 等待下一次预测
                time.sleep(self.prediction_interval)
                
            except Exception as e:
                logger.error(f"预测循环中发生错误: {e}")
                time.sleep(1)  # 错误后短暂等待
    
    def start(self):
        """启动实时预测引擎"""
        if self.is_running:
            logger.warning("预测引擎已在运行")
            return
        
        if self.lstm_model is None or self.classifier_model is None:
            logger.error("模型未加载，无法启动预测引擎")
            return
        
        logger.info("启动实时预测引擎...")
        self.is_running = True
        
        # 启动预测线程
        self.prediction_thread = threading.Thread(target=self._prediction_loop, daemon=True)
        self.prediction_thread.start()
        
        logger.info("实时预测引擎已启动")
    
    def stop(self):
        """停止实时预测引擎"""
        if not self.is_running:
            return
        
        logger.info("停止实时预测引擎...")
        self.is_running = False
        
        if self.prediction_thread:
            self.prediction_thread.join(timeout=5)
        
        logger.info("实时预测引擎已停止")
    
    def get_target_status(self) -> Dict:
        """获取所有目标的状态信息"""
        status = {}
        
        for target_id, buffer in self.target_buffers.items():
            if buffer:
                last_signal = buffer[-1]
                status[target_id] = {
                    "data_points": len(buffer),
                    "last_update": last_signal.timestamp,
                    "last_position": last_signal.position,
                    "last_velocity": last_signal.velocity,
                    "has_prediction": target_id in self.prediction_cache
                }
        
        return status
