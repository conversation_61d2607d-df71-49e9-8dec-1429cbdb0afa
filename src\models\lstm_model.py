"""
LSTM航迹预测模型
基于LSTM神经网络的时间序列预测模型
"""

import numpy as np
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
from typing import Tuple, Optional, List
from pathlib import Path

from config.model_config import LSTMConfig
from config.logging_config import default_logger as logger


class LSTMTrajectoryModel:
    """LSTM航迹预测模型"""
    
    def __init__(self, config: Optional[LSTMConfig] = None):
        """
        初始化LSTM模型
        
        Args:
            config: LSTM配置参数
        """
        self.config = config or LSTMConfig()
        self.model: Optional[keras.Model] = None
        self.is_trained = False
        
    def build_model(self) -> keras.Model:
        """构建增强LSTM模型架构（支持注意力机制）"""
        logger.info("构建增强LSTM模型架构...")

        # 输入层
        inputs = keras.Input(
            shape=(self.config.sequence_length, self.config.input_dim),
            name="trajectory_input"
        )

        # 输入特征分组处理
        # 位置特征 (6维)
        position_features = layers.Lambda(lambda x: x[:, :, :6], name="position_features")(inputs)
        # 运动特征 (6维)
        motion_features = layers.Lambda(lambda x: x[:, :, 6:12], name="motion_features")(inputs)
        # 姿态特征 (5维)
        attitude_features = layers.Lambda(lambda x: x[:, :, 12:17], name="attitude_features")(inputs)
        # 雷达特征 (5维)
        radar_features = layers.Lambda(lambda x: x[:, :, 17:], name="radar_features")(inputs)

        # 分别处理不同类型的特征
        position_lstm = layers.LSTM(64, return_sequences=True, name="position_lstm")(position_features)
        motion_lstm = layers.LSTM(64, return_sequences=True, name="motion_lstm")(motion_features)
        attitude_lstm = layers.LSTM(32, return_sequences=True, name="attitude_lstm")(attitude_features)
        radar_lstm = layers.LSTM(32, return_sequences=True, name="radar_lstm")(radar_features)

        # 特征融合
        fused_features = layers.Concatenate(axis=-1, name="feature_fusion")([
            position_lstm, motion_lstm, attitude_lstm, radar_lstm
        ])

        # 主LSTM层
        x = fused_features
        for i in range(self.config.num_layers):
            return_sequences = (i < self.config.num_layers - 1) or self.config.use_attention
            x = layers.LSTM(
                self.config.hidden_dim,
                return_sequences=return_sequences,
                dropout=self.config.dropout_rate,
                recurrent_dropout=0.1,
                name=f"main_lstm_{i+1}"
            )(x)

            # 添加批归一化
            if return_sequences:
                x = layers.BatchNormalization(name=f"bn_lstm_{i+1}")(x)

        # 注意力机制
        if self.config.use_attention:
            # 自注意力层
            attention_weights = layers.Dense(1, activation='tanh', name="attention_weights")(x)
            attention_weights = layers.Softmax(axis=1, name="attention_softmax")(attention_weights)

            # 加权平均
            x = layers.Multiply(name="attention_multiply")([x, attention_weights])
            x = layers.Lambda(lambda x: tf.reduce_sum(x, axis=1), name="attention_sum")(x)

        # 全连接层
        x = layers.Dense(
            self.config.hidden_dim,
            activation="relu",
            name="dense_1"
        )(x)

        x = layers.BatchNormalization(name="bn_dense_1")(x)
        x = layers.Dropout(self.config.dropout_rate)(x)

        x = layers.Dense(
            self.config.hidden_dim // 2,
            activation="relu",
            name="dense_2"
        )(x)

        x = layers.Dropout(self.config.dropout_rate)(x)

        # 输出层 - 预测位置和速度
        outputs = layers.Dense(
            self.config.output_dim * self.config.prediction_horizon,
            activation="linear",
            name="trajectory_output"
        )(x)

        # 重塑输出形状
        outputs = layers.Reshape(
            (self.config.prediction_horizon, self.config.output_dim),
            name="reshape_output"
        )(outputs)

        # 创建模型
        model = keras.Model(inputs=inputs, outputs=outputs, name="Enhanced_LSTM_Trajectory_Model")

        # 使用自定义损失函数
        def custom_loss(y_true, y_pred):
            # 位置损失（MSE）
            position_loss = tf.reduce_mean(tf.square(y_true[:, :, :3] - y_pred[:, :, :3]))
            # 速度损失（MAE）
            velocity_loss = tf.reduce_mean(tf.abs(y_true[:, :, 3:] - y_pred[:, :, 3:]))
            return position_loss + 0.5 * velocity_loss

        # 编译模型
        optimizer = keras.optimizers.AdamW(
            learning_rate=self.config.learning_rate,
            weight_decay=0.01
        )
        model.compile(
            optimizer=optimizer,
            loss=custom_loss,
            metrics=["mae", "mse"]
        )

        self.model = model
        logger.info(f"增强LSTM模型构建完成，参数量: {model.count_params()}")

        return model
    
    def train(self, X_train: np.ndarray, y_train: np.ndarray, 
              X_val: Optional[np.ndarray] = None, y_val: Optional[np.ndarray] = None) -> keras.callbacks.History:
        """
        训练LSTM模型
        
        Args:
            X_train: 训练输入数据 (batch_size, sequence_length, input_dim)
            y_train: 训练目标数据 (batch_size, prediction_horizon, output_dim)
            X_val: 验证输入数据
            y_val: 验证目标数据
            
        Returns:
            训练历史
        """
        if self.model is None:
            self.build_model()
        
        logger.info("开始训练LSTM模型...")
        
        # 设置回调函数
        callbacks = [
            keras.callbacks.EarlyStopping(
                monitor="val_loss",
                patience=self.config.early_stopping_patience,
                restore_best_weights=True
            ),
            keras.callbacks.ReduceLROnPlateau(
                monitor="val_loss",
                factor=0.5,
                patience=5,
                min_lr=1e-6
            )
        ]
        
        # 准备验证数据
        validation_data = None
        if X_val is not None and y_val is not None:
            validation_data = (X_val, y_val)
        
        # 训练模型
        history = self.model.fit(
            X_train, y_train,
            batch_size=self.config.batch_size,
            epochs=self.config.epochs,
            validation_data=validation_data,
            callbacks=callbacks,
            verbose=1
        )
        
        self.is_trained = True
        logger.info("LSTM模型训练完成")
        
        return history
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """
        预测航迹
        
        Args:
            X: 输入序列数据 (batch_size, sequence_length, input_dim)
            
        Returns:
            预测结果 (batch_size, prediction_horizon, output_dim)
        """
        if self.model is None:
            raise ValueError("模型未构建，请先调用build_model()或load_model()")
        
        if not self.is_trained:
            logger.warning("模型可能未训练，预测结果可能不准确")
        
        predictions = self.model.predict(X)
        return predictions
    
    def save_model(self, filepath: str):
        """保存模型"""
        if self.model is None:
            raise ValueError("模型未构建，无法保存")
        
        Path(filepath).parent.mkdir(parents=True, exist_ok=True)
        self.model.save(filepath)
        logger.info(f"模型已保存到: {filepath}")
    
    def load_model(self, filepath: str):
        """加载模型"""
        if not Path(filepath).exists():
            raise FileNotFoundError(f"模型文件不存在: {filepath}")
        
        self.model = keras.models.load_model(filepath)
        self.is_trained = True
        logger.info(f"模型已从 {filepath} 加载")
    
    def evaluate(self, X_test: np.ndarray, y_test: np.ndarray) -> dict:
        """评估模型性能"""
        if self.model is None:
            raise ValueError("模型未构建，无法评估")
        
        results = self.model.evaluate(X_test, y_test, verbose=0)
        
        metrics = {}
        for i, metric_name in enumerate(self.model.metrics_names):
            metrics[metric_name] = results[i]
        
        logger.info(f"模型评估结果: {metrics}")
        return metrics
    
    def get_model_summary(self) -> str:
        """获取模型摘要"""
        if self.model is None:
            return "模型未构建"
        
        import io
        stream = io.StringIO()
        self.model.summary(print_fn=lambda x: stream.write(x + '\n'))
        return stream.getvalue()
