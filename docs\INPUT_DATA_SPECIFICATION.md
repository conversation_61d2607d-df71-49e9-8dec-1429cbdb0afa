# 📊 AirModel_V0 输入数据技术规范

## 🎯 文档概述

本文档详细说明了AirModel_V0增强目标航迹ZZ跟踪实时预测模型的输入数据格式、结构定义、预处理要求和验证规则。系统支持27维增强特征向量、多站点协同观测和实时数据流处理。

---

## 📋 1. 数据结构定义

### 1.1 核心数据类：RadarSignal

**RadarSignal** 是系统的核心数据结构，包含27维完整特征向量：

```python
@dataclass
class RadarSignal:
    """雷达信号数据类（增强版）"""
    # 基础信息
    timestamp: datetime          # 时间戳
    station_id: str             # 雷达站点ID
    target_id: str              # 目标ID
    
    # 基本测量数据
    toa: float                  # 到达时间 (Time of Arrival)
    pa: float                   # 相位角 (Phase Angle)
    
    # 目标状态信息
    position: Tuple[float, float, float]           # 位置 (x, y, z)
    position_accuracy: Tuple[float, float, float]  # 位置精度 (σx, σy, σz)
    velocity: Tuple[float, float, float]           # 速度 (vx, vy, vz)
    acceleration: Tuple[float, float, float]       # 加速度 (ax, ay, az)
    
    # 姿态信息（完整）
    heading: float              # 航向角
    elevation: float            # 高度角
    pitch: float                # 俯仰角
    roll: float                 # 滚转角
    yaw: float                  # 偏航角
    
    # 雷达测量参数
    target_distance: float      # 目标距离
    pulse_width: float          # 脉冲宽度
    radar_frequency: float      # 雷达工作频率
    
    # 信号特征
    prf: float                  # 重复频率 (Pulse Repetition Frequency)
    signal_strength: float      # 信号强度
    communication_status: int   # 通信状态标识
    
    # 信号质量
    snr: float                  # 信噪比
    confidence: float           # 置信度
```

### 1.2 27维特征向量详细说明

| 维度 | 特征名称 | 数据类型 | 取值范围 | 单位 | 说明 |
|------|----------|----------|----------|------|------|
| **位置信息 (6维)** |
| 1 | position_x | float | [-50000, 50000] | 米 | X坐标位置 |
| 2 | position_y | float | [-50000, 50000] | 米 | Y坐标位置 |
| 3 | position_z | float | [0, 30000] | 米 | Z坐标位置（高度） |
| 4 | position_accuracy_x | float | [1.0, 100.0] | 米 | X方向位置精度 |
| 5 | position_accuracy_y | float | [1.0, 100.0] | 米 | Y方向位置精度 |
| 6 | position_accuracy_z | float | [5.0, 200.0] | 米 | Z方向位置精度 |
| **运动参数 (6维)** |
| 7 | velocity_x | float | [-1000, 1000] | 米/秒 | X方向速度分量 |
| 8 | velocity_y | float | [-1000, 1000] | 米/秒 | Y方向速度分量 |
| 9 | velocity_z | float | [-500, 500] | 米/秒 | Z方向速度分量 |
| 10 | acceleration_x | float | [-50, 50] | 米/秒² | X方向加速度分量 |
| 11 | acceleration_y | float | [-50, 50] | 米/秒² | Y方向加速度分量 |
| 12 | acceleration_z | float | [-30, 30] | 米/秒² | Z方向加速度分量 |
| **姿态信息 (5维)** |
| 13 | heading | float | [0, 2π] | 弧度 | 航向角 |
| 14 | elevation | float | [-π/2, π/2] | 弧度 | 高度角 |
| 15 | pitch | float | [-π/2, π/2] | 弧度 | 俯仰角 |
| 16 | roll | float | [-π, π] | 弧度 | 滚转角 |
| 17 | yaw | float | [-π, π] | 弧度 | 偏航角 |
| **雷达测量参数 (3维)** |
| 18 | target_distance | float | [100, 100000] | 米 | 目标距离 |
| 19 | pulse_width | float | [0.1, 10.0] | 微秒 | 脉冲宽度 |
| 20 | radar_frequency | float | [1000, 10000] | MHz | 雷达工作频率 |
| **信号特征 (3维)** |
| 21 | prf | float | [100, 10000] | Hz | 脉冲重复频率 |
| 22 | signal_strength | float | [0.0, 1.0] | 无量纲 | 信号强度 |
| 23 | communication_status | int | {0, 1, 2} | 枚举 | 通信状态 |
| **基本测量和质量 (4维)** |
| 24 | toa | float | [0.001, 1.0] | 秒 | 到达时间 |
| 25 | pa | float | [0, 2π] | 弧度 | 相位角 |
| 26 | snr | float | [0, 50] | dB | 信噪比 |
| 27 | confidence | float | [0.0, 1.0] | 无量纲 | 置信度 |

### 1.3 通信状态枚举说明

| 值 | 状态名称 | 说明 |
|----|----------|------|
| 0 | 正常 | 通信正常，无干扰 |
| 1 | 干扰 | 存在电子干扰 |
| 2 | 中断 | 通信中断或严重干扰 |

---

## 🌐 2. 多站点数据格式

### 2.1 雷达站点定义

```python
@dataclass
class RadarStation:
    """雷达站点类"""
    station_id: str                           # 站点唯一标识符
    name: str                                 # 站点名称
    position: Tuple[float, float, float]      # 站点位置 (x, y, z)
    detection_range: float                    # 探测范围（km）
    frequency: float                          # 工作频率（MHz）
    is_active: bool = True                    # 是否激活
```

### 2.2 站点标识符规范

**格式**: `radar_XXX`
- **前缀**: 固定为 `radar_`
- **编号**: 3位数字，从001开始
- **示例**: `radar_001`, `radar_002`, `radar_003`

**特殊站点标识符**:
- `FUSED`: 多站点融合后的虚拟站点
- `AWACS_XXX`: 预警机站点，如 `AWACS_001`

### 2.3 TOA/PA数据格式

#### TOA (到达时间) 数据
- **数据类型**: `float`
- **取值范围**: [0.001, 1.0] 秒
- **精度要求**: 微秒级 (1e-6秒)
- **物理意义**: 信号从发射到接收的时间延迟

#### PA (相位角) 数据
- **数据类型**: `float`
- **取值范围**: [0, 2π] 弧度
- **精度要求**: 毫弧度级 (1e-3弧度)
- **物理意义**: 信号的相位偏移角度

### 2.4 时间戳要求

**格式**: ISO 8601标准
```
YYYY-MM-DDTHH:MM:SS.fffffZ
```

**示例**:
```
2025-09-03T04:16:00.123456Z
```

**同步要求**:
- **时间窗口**: 0.1-0.2秒内的信号视为同步
- **最大偏差**: ±50毫秒
- **时钟同步**: 建议使用GPS或NTP同步

---

## 🔧 3. 数据预处理要求

### 3.1 归一化方法

#### MinMax归一化 (默认)
```python
normalized_value = (value - min_value) / (max_value - min_value)
```

**适用特征**:
- 位置坐标 (x, y, z)
- 速度分量 (vx, vy, vz)
- 信号强度、置信度

#### Z-score标准化
```python
normalized_value = (value - mean) / std_deviation
```

**适用特征**:
- 加速度分量 (ax, ay, az)
- 姿态角度 (heading, pitch, roll, yaw)
- 信噪比 (SNR)

### 3.2 异常值处理

#### 3σ原则检测
```python
def detect_outliers(data, threshold=3.0):
    mean = np.mean(data)
    std = np.std(data)
    outliers = np.abs(data - mean) > threshold * std
    return outliers
```

#### 异常值处理策略
1. **删除**: 严重异常值直接删除
2. **截断**: 超出合理范围的值截断到边界
3. **插值**: 使用前后值进行线性插值
4. **标记**: 保留但降低置信度

### 3.3 时序对齐算法

#### 多站点时间窗口对齐
```python
def align_signals_by_time(signals, time_window=0.1):
    """
    按时间窗口对齐多站点信号
    
    Args:
        signals: 信号列表
        time_window: 时间窗口（秒）
    
    Returns:
        时间对齐的信号组列表
    """
    sorted_signals = sorted(signals, key=lambda x: x.timestamp)
    aligned_groups = []
    current_group = [sorted_signals[0]]
    base_time = sorted_signals[0].timestamp
    
    for signal in sorted_signals[1:]:
        time_diff = (signal.timestamp - base_time).total_seconds()
        
        if time_diff <= time_window:
            current_group.append(signal)
        else:
            if len(current_group) >= min_stations:
                aligned_groups.append(current_group)
            current_group = [signal]
            base_time = signal.timestamp
    
    return aligned_groups
```

#### 数据融合算法
```python
def fuse_toa_pa_data(station_signals):
    """
    融合多站点的TOA和PA数据
    
    Args:
        station_signals: 同一时间窗口内的多站点信号
    
    Returns:
        (融合TOA, 融合PA, 融合置信度)
    """
    toa_values = [signal.toa for signal in station_signals]
    pa_values = [signal.pa for signal in station_signals]
    confidences = [signal.confidence for signal in station_signals]
    
    # 置信度加权平均
    total_confidence = sum(confidences)
    weights = [conf / total_confidence for conf in confidences]
    
    fused_toa = sum(toa * w for toa, w in zip(toa_values, weights))
    fused_pa = sum(pa * w for pa, w in zip(pa_values, weights))
    fusion_confidence = min(1.0, total_confidence / len(station_signals))
    
    return fused_toa, fused_pa, fusion_confidence
```

---

## 🔌 4. API输入格式

### 4.1 REST API输入格式

#### 添加雷达信号 - POST /radar/signal

**JSON Schema**:
```json
{
  "type": "object",
  "required": [
    "timestamp", "station_id", "target_id", 
    "toa", "pa", "position", "velocity"
  ],
  "properties": {
    "timestamp": {
      "type": "string",
      "format": "date-time",
      "description": "时间戳 (ISO格式)"
    },
    "station_id": {
      "type": "string",
      "pattern": "^radar_\\d{3}$",
      "description": "雷达站点ID"
    },
    "target_id": {
      "type": "string",
      "pattern": "^target_\\d{3}$",
      "description": "目标ID"
    },
    "toa": {
      "type": "number",
      "minimum": 0.001,
      "maximum": 1.0,
      "description": "到达时间"
    },
    "pa": {
      "type": "number",
      "minimum": 0,
      "maximum": 6.283185,
      "description": "相位角"
    },
    "position": {
      "type": "array",
      "items": {"type": "number"},
      "minItems": 3,
      "maxItems": 3,
      "description": "位置坐标 [x, y, z]"
    },
    "position_accuracy": {
      "type": "array",
      "items": {"type": "number", "minimum": 1.0, "maximum": 100.0},
      "minItems": 3,
      "maxItems": 3,
      "description": "位置精度 [σx, σy, σz]"
    },
    "velocity": {
      "type": "array",
      "items": {"type": "number"},
      "minItems": 3,
      "maxItems": 3,
      "description": "速度向量 [vx, vy, vz]"
    },
    "acceleration": {
      "type": "array",
      "items": {"type": "number"},
      "minItems": 3,
      "maxItems": 3,
      "description": "加速度向量 [ax, ay, az]"
    },
    "heading": {
      "type": "number",
      "minimum": 0,
      "maximum": 6.283185,
      "default": 0.0,
      "description": "航向角"
    },
    "elevation": {
      "type": "number",
      "minimum": -1.570796,
      "maximum": 1.570796,
      "default": 0.0,
      "description": "高度角"
    },
    "pitch": {
      "type": "number",
      "minimum": -1.570796,
      "maximum": 1.570796,
      "default": 0.0,
      "description": "俯仰角"
    },
    "roll": {
      "type": "number",
      "minimum": -3.141593,
      "maximum": 3.141593,
      "default": 0.0,
      "description": "滚转角"
    },
    "yaw": {
      "type": "number",
      "minimum": -3.141593,
      "maximum": 3.141593,
      "default": 0.0,
      "description": "偏航角"
    },
    "target_distance": {
      "type": "number",
      "minimum": 100,
      "maximum": 100000,
      "description": "目标距离"
    },
    "pulse_width": {
      "type": "number",
      "minimum": 0.1,
      "maximum": 10.0,
      "description": "脉冲宽度"
    },
    "radar_frequency": {
      "type": "number",
      "minimum": 1000,
      "maximum": 10000,
      "description": "雷达工作频率"
    },
    "prf": {
      "type": "number",
      "minimum": 100,
      "maximum": 10000,
      "description": "脉冲重复频率"
    },
    "signal_strength": {
      "type": "number",
      "minimum": 0.0,
      "maximum": 1.0,
      "description": "信号强度"
    },
    "communication_status": {
      "type": "integer",
      "enum": [0, 1, 2],
      "default": 0,
      "description": "通信状态"
    },
    "snr": {
      "type": "number",
      "minimum": 0,
      "maximum": 50,
      "default": 20.0,
      "description": "信噪比"
    },
    "confidence": {
      "type": "number",
      "minimum": 0.0,
      "maximum": 1.0,
      "default": 0.9,
      "description": "置信度"
    }
  }
}
```

#### 请求示例
```json
{
  "timestamp": "2025-09-03T04:16:00.123456Z",
  "station_id": "radar_001",
  "target_id": "target_001",
  "toa": 0.15,
  "pa": 0.785,
  "position": [5000.0, 3000.0, 8000.0],
  "position_accuracy": [10.0, 12.0, 20.0],
  "velocity": [250.0, 150.0, 30.0],
  "acceleration": [5.0, -2.0, 1.0],
  "heading": 0.524,
  "elevation": 0.118,
  "pitch": 0.1,
  "roll": -0.05,
  "yaw": 0.02,
  "target_distance": 9434.0,
  "pulse_width": 1.2,
  "radar_frequency": 3000.0,
  "prf": 2500.0,
  "signal_strength": 0.85,
  "communication_status": 0,
  "snr": 20.5,
  "confidence": 0.92
}
```

### 4.2 WebSocket输入格式

#### 连接端点
```
ws://localhost:8001/ws
```

#### 消息格式
```json
{
  "type": "radar_signal",
  "data": {
    // 与REST API相同的RadarSignal数据结构
  }
}
```

#### 批量数据格式
```json
{
  "type": "batch_signals",
  "data": [
    {
      // RadarSignal 1
    },
    {
      // RadarSignal 2
    }
  ]
}
```

### 4.3 必填字段和可选字段

#### 必填字段 (Required)
- `timestamp`: 时间戳
- `station_id`: 雷达站点ID
- `target_id`: 目标ID
- `toa`: 到达时间
- `pa`: 相位角
- `position`: 位置坐标 [x, y, z]
- `velocity`: 速度向量 [vx, vy, vz]

#### 可选字段 (Optional)
- `position_accuracy`: 位置精度，默认 [10.0, 10.0, 20.0]
- `acceleration`: 加速度，默认 [0.0, 0.0, 0.0]
- `heading`: 航向角，默认 0.0
- `elevation`: 高度角，默认 0.0
- `pitch`: 俯仰角，默认 0.0
- `roll`: 滚转角，默认 0.0
- `yaw`: 偏航角，默认 0.0
- `target_distance`: 目标距离，自动计算
- `pulse_width`: 脉冲宽度，默认 1.0
- `radar_frequency`: 雷达频率，默认 3000.0
- `prf`: 脉冲重复频率，默认 2000.0
- `signal_strength`: 信号强度，默认 0.8
- `communication_status`: 通信状态，默认 0
- `snr`: 信噪比，默认 20.0
- `confidence`: 置信度，默认 0.9

---

## ✅ 5. 数据验证规则

### 5.1 基础验证规则

#### 时间戳验证
```python
def validate_timestamp(timestamp_str: str) -> bool:
    """验证时间戳格式"""
    try:
        datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
        return True
    except ValueError:
        return False
```

#### 数值范围验证
```python
VALIDATION_RULES = {
    'toa': {'min': 0.001, 'max': 1.0},
    'pa': {'min': 0.0, 'max': 2 * np.pi},
    'position_x': {'min': -50000, 'max': 50000},
    'position_y': {'min': -50000, 'max': 50000},
    'position_z': {'min': 0, 'max': 30000},
    'velocity_x': {'min': -1000, 'max': 1000},
    'velocity_y': {'min': -1000, 'max': 1000},
    'velocity_z': {'min': -500, 'max': 500},
    'acceleration_x': {'min': -50, 'max': 50},
    'acceleration_y': {'min': -50, 'max': 50},
    'acceleration_z': {'min': -30, 'max': 30},
    'heading': {'min': 0.0, 'max': 2 * np.pi},
    'elevation': {'min': -np.pi/2, 'max': np.pi/2},
    'pitch': {'min': -np.pi/2, 'max': np.pi/2},
    'roll': {'min': -np.pi, 'max': np.pi},
    'yaw': {'min': -np.pi, 'max': np.pi},
    'target_distance': {'min': 100, 'max': 100000},
    'pulse_width': {'min': 0.1, 'max': 10.0},
    'radar_frequency': {'min': 1000, 'max': 10000},
    'prf': {'min': 100, 'max': 10000},
    'signal_strength': {'min': 0.0, 'max': 1.0},
    'snr': {'min': 0, 'max': 50},
    'confidence': {'min': 0.0, 'max': 1.0}
}
```

#### 逻辑一致性验证
```python
def validate_logical_consistency(signal: RadarSignal) -> List[str]:
    """验证数据逻辑一致性"""
    errors = []

    # 1. 速度与位置变化一致性
    speed = np.linalg.norm(signal.velocity)
    if speed > 1000:  # 超音速检查
        errors.append("速度超过合理范围")

    # 2. 高度与高度角一致性
    if signal.position[2] < 100 and signal.elevation > np.pi/6:
        errors.append("低空目标高度角过大")

    # 3. 信号强度与距离一致性
    if signal.target_distance > 50000 and signal.signal_strength > 0.8:
        errors.append("远距离目标信号强度过高")

    # 4. 置信度与信噪比一致性
    if signal.snr < 10 and signal.confidence > 0.8:
        errors.append("低信噪比下置信度过高")

    return errors
```

### 5.2 多站点数据验证

#### 站点ID格式验证
```python
def validate_station_id(station_id: str) -> bool:
    """验证站点ID格式"""
    import re
    pattern = r'^(radar_\d{3}|FUSED|AWACS_\d{3})$'
    return bool(re.match(pattern, station_id))
```

#### 时间同步验证
```python
def validate_time_sync(signals: List[RadarSignal],
                      time_window: float = 0.1) -> bool:
    """验证多站点时间同步"""
    if len(signals) < 2:
        return True

    timestamps = [s.timestamp for s in signals]
    time_diffs = [(t2 - t1).total_seconds()
                  for t1, t2 in zip(timestamps[:-1], timestamps[1:])]

    return all(diff <= time_window for diff in time_diffs)
```

### 5.3 错误处理机制

#### 错误分类
```python
class ValidationError(Exception):
    """数据验证错误"""
    def __init__(self, field: str, value: any, message: str):
        self.field = field
        self.value = value
        self.message = message
        super().__init__(f"字段 '{field}' 验证失败: {message}")

class DataFormatError(Exception):
    """数据格式错误"""
    pass

class TimeoutError(Exception):
    """数据超时错误"""
    pass
```

#### 错误响应格式
```json
{
  "error": {
    "type": "ValidationError",
    "field": "toa",
    "value": 1.5,
    "message": "TOA值超出有效范围 [0.001, 1.0]",
    "timestamp": "2025-09-03T04:16:00.000Z"
  }
}
```

#### 错误处理策略
1. **严重错误**: 拒绝数据，返回错误信息
2. **警告错误**: 接受数据，记录警告日志
3. **自动修复**: 自动修正明显错误的数据
4. **降级处理**: 降低数据置信度但继续处理

---

## 📝 6. 示例数据

### 6.1 单目标场景示例

#### 战斗机巡逻任务
```json
{
  "timestamp": "2025-09-03T04:16:00.000Z",
  "station_id": "radar_001",
  "target_id": "target_001",
  "toa": 0.12,
  "pa": 1.047,
  "position": [8000.0, 5000.0, 10000.0],
  "position_accuracy": [8.0, 10.0, 15.0],
  "velocity": [180.0, 120.0, 0.0],
  "acceleration": [2.0, -1.0, 0.5],
  "heading": 0.588,
  "elevation": 0.0,
  "pitch": 0.05,
  "roll": 0.02,
  "yaw": 0.01,
  "target_distance": 12207.0,
  "pulse_width": 1.0,
  "radar_frequency": 3000.0,
  "prf": 2000.0,
  "signal_strength": 0.82,
  "communication_status": 0,
  "snr": 22.5,
  "confidence": 0.91
}
```

#### 预警机探测任务
```json
{
  "timestamp": "2025-09-03T04:16:01.000Z",
  "station_id": "radar_002",
  "target_id": "awacs_001",
  "toa": 0.18,
  "pa": 2.094,
  "position": [15000.0, 8000.0, 12000.0],
  "position_accuracy": [12.0, 15.0, 25.0],
  "velocity": [120.0, 80.0, 5.0],
  "acceleration": [0.5, 0.2, 0.1],
  "heading": 0.785,
  "elevation": 0.087,
  "pitch": 0.02,
  "roll": -0.01,
  "yaw": 0.005,
  "target_distance": 19209.0,
  "pulse_width": 2.5,
  "radar_frequency": 2800.0,
  "prf": 1500.0,
  "signal_strength": 0.75,
  "communication_status": 0,
  "snr": 18.2,
  "confidence": 0.88
}
```

### 6.2 多目标场景示例

#### 空中格斗场景
```json
[
  {
    "timestamp": "2025-09-03T04:16:00.000Z",
    "station_id": "radar_001",
    "target_id": "fighter_001",
    "toa": 0.08,
    "pa": 0.524,
    "position": [3000.0, 2000.0, 8000.0],
    "velocity": [300.0, 200.0, 50.0],
    "acceleration": [15.0, -10.0, 5.0],
    "heading": 0.785,
    "pitch": 0.2,
    "roll": 0.3,
    "yaw": 0.1,
    "signal_strength": 0.9,
    "communication_status": 1,
    "snr": 25.0,
    "confidence": 0.85
  },
  {
    "timestamp": "2025-09-03T04:16:00.100Z",
    "station_id": "radar_001",
    "target_id": "fighter_002",
    "toa": 0.09,
    "pa": 1.047,
    "position": [3500.0, 1800.0, 7500.0],
    "velocity": [280.0, -150.0, -20.0],
    "acceleration": [-8.0, 12.0, -3.0],
    "heading": 2.356,
    "pitch": -0.15,
    "roll": -0.25,
    "yaw": -0.08,
    "signal_strength": 0.88,
    "communication_status": 1,
    "snr": 23.5,
    "confidence": 0.83
  }
]
```

### 6.3 多站点协同观测示例

#### 三站点同时观测同一目标
```json
[
  {
    "timestamp": "2025-09-03T04:16:00.000Z",
    "station_id": "radar_001",
    "target_id": "target_001",
    "toa": 0.15,
    "pa": 0.785,
    "position": [5000.0, 3000.0, 8000.0],
    "velocity": [250.0, 150.0, 30.0],
    "snr": 22.0,
    "confidence": 0.92
  },
  {
    "timestamp": "2025-09-03T04:16:00.050Z",
    "station_id": "radar_002",
    "target_id": "target_001",
    "toa": 0.12,
    "pa": 0.820,
    "position": [5020.0, 2980.0, 8010.0],
    "velocity": [248.0, 152.0, 28.0],
    "snr": 20.5,
    "confidence": 0.89
  },
  {
    "timestamp": "2025-09-03T04:16:00.080Z",
    "station_id": "radar_003",
    "target_id": "target_001",
    "toa": 0.18,
    "pa": 0.750,
    "position": [4980.0, 3020.0, 7990.0],
    "velocity": [252.0, 148.0, 32.0],
    "snr": 24.2,
    "confidence": 0.94
  }
]
```

### 6.4 时序数据示例

#### 20个时间步的完整航迹
```python
# 生成时序数据示例
import numpy as np
from datetime import datetime, timedelta

def generate_trajectory_sequence():
    """生成20个时间步的航迹序列"""
    base_time = datetime.now()
    base_pos = np.array([5000.0, 3000.0, 8000.0])
    base_vel = np.array([200.0, 100.0, 20.0])

    sequence = []
    for i in range(20):
        # 更新位置和速度
        current_pos = base_pos + base_vel * i + np.random.normal(0, 10, 3)
        current_vel = base_vel + np.random.normal(0, 5, 3)

        signal = {
            "timestamp": (base_time + timedelta(seconds=i)).isoformat() + "Z",
            "station_id": "radar_001",
            "target_id": "target_001",
            "toa": 0.1 + i * 0.005 + np.random.normal(0, 0.001),
            "pa": np.pi/4 + i * 0.01 + np.random.normal(0, 0.005),
            "position": current_pos.tolist(),
            "velocity": current_vel.tolist(),
            "acceleration": np.random.normal(0, 2, 3).tolist(),
            "heading": np.pi/3 + i * 0.02,
            "snr": 20.0 + np.random.normal(0, 2),
            "confidence": 0.9 + np.random.normal(0, 0.05)
        }
        sequence.append(signal)

    return sequence
```

---

## 🔍 7. 数据质量检查

### 7.1 完整性检查
- **必填字段**: 确保所有必填字段都存在
- **数据类型**: 验证每个字段的数据类型正确
- **数组长度**: 确保位置、速度等数组长度为3

### 7.2 准确性检查
- **数值范围**: 所有数值都在合理范围内
- **物理约束**: 遵循物理定律（如速度连续性）
- **逻辑一致**: 相关字段之间逻辑一致

### 7.3 时效性检查
- **时间戳**: 不能是未来时间
- **数据延迟**: 数据延迟不超过设定阈值
- **序列连续**: 时序数据时间间隔合理

### 7.4 一致性检查
- **多站点**: 同一目标的多站点数据一致
- **历史数据**: 与历史数据趋势一致
- **物理模型**: 符合目标运动物理模型

---

## 📚 8. 参考资料

### 8.1 相关标准
- **ISO 8601**: 时间戳格式标准
- **JSON Schema**: JSON数据验证标准
- **IEEE 754**: 浮点数表示标准

### 8.2 技术文档
- [API文档](docs/api.md)
- [增强模型指南](docs/ENHANCED_MODEL_GUIDE.md)
- [部署指南](docs/deployment.md)

### 8.3 示例代码
- [快速演示](examples/quick_demo.py)
- [API演示](examples/api_demo.py)
- [多站点演示](examples/multi_station_demo.py)

---

## 📞 技术支持

如有任何关于输入数据格式的问题，请参考：
1. 项目README.md文档
2. examples/目录下的示例代码
3. 运行 `python scripts/test_core_functions.py` 进行数据结构测试

**文档版本**: v3.0
**最后更新**: 2025-09-03
**维护者**: AirModel_V0开发团队
