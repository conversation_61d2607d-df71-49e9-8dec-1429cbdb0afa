"""
简化的增强模型训练脚本
训练支持8类战术任务的增强分类器
"""

import sys
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
from sklearn.utils.class_weight import compute_class_weight

# 设置日志
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_enhanced_classifier(input_dim=15, num_classes=8):
    """创建增强战术分类器"""
    logger.info("构建增强战术分类器...")
    
    inputs = keras.Input(shape=(input_dim,), name="tactical_features")
    
    # 特征预处理
    x = layers.BatchNormalization(name="input_bn")(inputs)
    
    # 多层感知机
    x = layers.Dense(128, activation="relu", kernel_regularizer=keras.regularizers.l2(0.01), name="hidden_1")(x)
    x = layers.BatchNormalization(name="bn_1")(x)
    x = layers.Dropout(0.4)(x)
    
    x = layers.Dense(64, activation="relu", kernel_regularizer=keras.regularizers.l2(0.01), name="hidden_2")(x)
    x = layers.BatchNormalization(name="bn_2")(x)
    x = layers.Dropout(0.4)(x)
    
    x = layers.Dense(32, activation="relu", kernel_regularizer=keras.regularizers.l2(0.01), name="hidden_3")(x)
    x = layers.Dropout(0.3)(x)
    
    # 输出层
    outputs = layers.Dense(num_classes, activation="softmax", name="tactical_output")(x)
    
    model = keras.Model(inputs=inputs, outputs=outputs, name="Enhanced_Tactical_Classifier")
    
    # 编译模型
    optimizer = keras.optimizers.AdamW(learning_rate=0.0005, weight_decay=0.01)
    model.compile(
        optimizer=optimizer,
        loss="categorical_crossentropy",
        metrics=["accuracy", "top_k_categorical_accuracy"]
    )
    
    logger.info(f"增强分类器构建完成，参数量: {model.count_params()}")
    return model


def generate_enhanced_tactical_data(num_samples=800):
    """生成8类战术任务的增强特征数据"""
    logger.info("生成8类战术任务数据...")
    
    tactical_modes = [
        "巡逻任务", "预警探测", "电子侦察", "电子干扰",
        "对空攻击", "对地攻击", "空中格斗", "撤退规避"
    ]
    
    X = []
    y = []
    
    samples_per_class = num_samples // len(tactical_modes)
    
    for mode_idx, mode in enumerate(tactical_modes):
        logger.info(f"生成 {mode} 数据...")
        
        for i in range(samples_per_class):
            # 根据战术模式生成特征
            if mode == "巡逻任务":
                features = [
                    np.random.normal(150, 20),    # 平均速度
                    np.random.normal(15, 5),      # 速度标准差
                    np.random.normal(180, 30),    # 最大速度
                    np.random.normal(2, 1),       # 平均加速度
                    np.random.normal(8, 3),       # 最大加速度
                    np.random.normal(25, 10),     # 机动性指标
                    np.random.normal(10000, 200), # 平均高度
                    np.random.normal(300, 100),   # 高度变化
                    np.random.normal(5, 15),      # 高度趋势
                    np.random.normal(0.05, 0.02), # 航向变化
                    np.random.normal(0.15, 0.05), # 姿态变化
                    0,                            # 预留特征
                    np.random.normal(0.8, 0.1),   # 信号强度
                    np.random.normal(0.1, 0.05),  # 通信干扰
                    np.random.normal(20, 3)       # 信号质量
                ]
            elif mode == "预警探测":
                features = [
                    np.random.normal(120, 15),    # 较低速度
                    np.random.normal(10, 3),      # 速度稳定
                    np.random.normal(150, 20),    # 最大速度
                    np.random.normal(1, 0.5),     # 低加速度
                    np.random.normal(5, 2),       # 最大加速度
                    np.random.normal(15, 5),      # 低机动性
                    np.random.normal(15000, 500), # 高空
                    np.random.normal(800, 200),   # 高度变化大
                    np.random.normal(20, 30),     # 高度趋势
                    np.random.normal(0.03, 0.01), # 航向变化小
                    np.random.normal(0.1, 0.03),  # 姿态变化小
                    0,
                    np.random.normal(0.9, 0.05),  # 高信号强度
                    np.random.normal(0.05, 0.02), # 低通信干扰
                    np.random.normal(22, 2)       # 高信号质量
                ]
            elif mode == "电子侦察":
                features = [
                    np.random.normal(200, 30),    # 中等速度
                    np.random.normal(25, 8),      # 速度变化
                    np.random.normal(250, 40),    # 最大速度
                    np.random.normal(3, 1.5),     # 中等加速度
                    np.random.normal(12, 4),      # 最大加速度
                    np.random.normal(35, 15),     # 中等机动性
                    np.random.normal(12000, 800), # 中高空
                    np.random.normal(1200, 300),  # 高度变化
                    np.random.normal(10, 40),     # 高度趋势
                    np.random.normal(0.08, 0.03), # 航向变化
                    np.random.normal(0.2, 0.08),  # 姿态变化
                    0,
                    np.random.normal(0.7, 0.15),  # 中等信号强度
                    np.random.normal(0.15, 0.08), # 中等通信干扰
                    np.random.normal(18, 4)       # 中等信号质量
                ]
            elif mode == "电子干扰":
                features = [
                    np.random.normal(80, 25),     # 低速度
                    np.random.normal(20, 8),      # 速度变化
                    np.random.normal(120, 30),    # 最大速度
                    np.random.normal(1.5, 0.8),   # 低加速度
                    np.random.normal(6, 2),       # 最大加速度
                    np.random.normal(25, 10),     # 低机动性
                    np.random.normal(11000, 400), # 中高空
                    np.random.normal(600, 200),   # 高度变化
                    np.random.normal(0, 20),      # 高度趋势
                    np.random.normal(0.04, 0.02), # 航向变化小
                    np.random.normal(0.12, 0.05), # 姿态变化小
                    0,
                    np.random.normal(0.5, 0.2),   # 低信号强度（干扰影响）
                    np.random.normal(0.4, 0.15),  # 高通信干扰
                    np.random.normal(15, 5)       # 低信号质量
                ]
            elif mode == "对空攻击":
                features = [
                    np.random.normal(400, 60),    # 高速度
                    np.random.normal(40, 15),     # 速度变化大
                    np.random.normal(500, 80),    # 高最大速度
                    np.random.normal(8, 3),       # 高加速度
                    np.random.normal(25, 8),      # 高最大加速度
                    np.random.normal(70, 25),     # 高机动性
                    np.random.normal(8000, 1000), # 中等高度
                    np.random.normal(2000, 500),  # 高度变化大
                    np.random.normal(50, 80),     # 爬升趋势
                    np.random.normal(0.15, 0.05), # 航向变化大
                    np.random.normal(0.3, 0.1),   # 姿态变化大
                    0,
                    np.random.normal(0.85, 0.1),  # 高信号强度
                    np.random.normal(0.08, 0.04), # 低通信干扰
                    np.random.normal(19, 3)       # 中等信号质量
                ]
            elif mode == "对地攻击":
                features = [
                    np.random.normal(300, 50),    # 中高速度
                    np.random.normal(35, 12),     # 速度变化
                    np.random.normal(400, 60),    # 最大速度
                    np.random.normal(6, 2.5),     # 中高加速度
                    np.random.normal(20, 6),      # 最大加速度
                    np.random.normal(55, 20),     # 中高机动性
                    np.random.normal(5000, 800),  # 低空
                    np.random.normal(1500, 400),  # 高度变化
                    np.random.normal(-30, 50),    # 下降趋势
                    np.random.normal(0.1, 0.04),  # 航向变化
                    np.random.normal(0.25, 0.08), # 姿态变化
                    0,
                    np.random.normal(0.75, 0.12), # 中高信号强度
                    np.random.normal(0.12, 0.06), # 中等通信干扰
                    np.random.normal(17, 4)       # 中等信号质量
                ]
            elif mode == "空中格斗":
                features = [
                    np.random.normal(350, 80),    # 高变化速度
                    np.random.normal(60, 20),     # 速度变化很大
                    np.random.normal(450, 100),   # 高最大速度
                    np.random.normal(12, 5),      # 很高加速度
                    np.random.normal(35, 12),     # 很高最大加速度
                    np.random.normal(100, 30),    # 很高机动性
                    np.random.normal(8000, 1500), # 中等高度
                    np.random.normal(3000, 800),  # 高度变化很大
                    np.random.normal(0, 100),     # 高度趋势随机
                    np.random.normal(0.25, 0.1),  # 航向变化很大
                    np.random.normal(0.5, 0.15),  # 姿态变化很大
                    0,
                    np.random.normal(0.6, 0.2),   # 中等信号强度
                    np.random.normal(0.2, 0.1),   # 中等通信干扰
                    np.random.normal(16, 5)       # 中低信号质量
                ]
            else:  # 撤退规避
                features = [
                    np.random.normal(450, 70),    # 很高速度
                    np.random.normal(50, 18),     # 速度变化大
                    np.random.normal(600, 100),   # 很高最大速度
                    np.random.normal(10, 4),      # 高加速度
                    np.random.normal(30, 10),     # 高最大加速度
                    np.random.normal(80, 25),     # 高机动性
                    np.random.normal(6000, 1200), # 中低空
                    np.random.normal(2500, 600),  # 高度变化大
                    np.random.normal(80, 60),     # 爬升趋势
                    np.random.normal(0.2, 0.08),  # 航向变化大
                    np.random.normal(0.4, 0.12),  # 姿态变化大
                    0,
                    np.random.normal(0.7, 0.15),  # 中等信号强度
                    np.random.normal(0.25, 0.1),  # 高通信干扰
                    np.random.normal(15, 4)       # 中低信号质量
                ]
            
            X.append(features)
            
            # 创建one-hot标签
            label = np.zeros(len(tactical_modes))
            label[mode_idx] = 1
            y.append(label)
    
    return np.array(X), np.array(y), tactical_modes


def main():
    """主训练函数"""
    logger.info("开始增强分类器训练...")
    
    # 创建保存目录
    model_save_path = Path("data/models")
    model_save_path.mkdir(parents=True, exist_ok=True)
    
    # 生成训练数据
    X, y, class_names = generate_enhanced_tactical_data()
    
    # 数据分割
    split_idx = int(len(X) * 0.7)
    X_train, X_val = X[:split_idx], X[split_idx:]
    y_train, y_val = y[:split_idx], y[split_idx:]
    
    logger.info(f"训练数据: {X_train.shape}, 验证数据: {X_val.shape}")
    logger.info(f"8类战术任务: {class_names}")
    
    # 计算类别权重
    y_train_labels = [class_names[np.argmax(label)] for label in y_train]
    y_numeric = [np.argmax(label) for label in y_train]
    
    class_weights = compute_class_weight(
        class_weight='balanced',
        classes=np.unique(y_numeric),
        y=y_numeric
    )
    class_weight_dict = {i: weight for i, weight in enumerate(class_weights)}
    
    logger.info(f"类别权重: {class_weight_dict}")
    
    # 创建模型
    model = create_enhanced_classifier(input_dim=X.shape[1], num_classes=len(class_names))
    
    # 设置回调函数
    callbacks = [
        keras.callbacks.EarlyStopping(
            monitor="val_accuracy",
            patience=15,
            restore_best_weights=True,
            mode="max"
        ),
        keras.callbacks.ReduceLROnPlateau(
            monitor="val_loss",
            factor=0.5,
            patience=8,
            min_lr=1e-7
        ),
        keras.callbacks.ModelCheckpoint(
            filepath=str(model_save_path / "best_enhanced_classifier.h5"),
            monitor="val_accuracy",
            save_best_only=True,
            mode="max"
        )
    ]
    
    # 训练模型
    logger.info("开始训练增强分类器...")
    history = model.fit(
        X_train, y_train,
        batch_size=64,
        epochs=100,
        validation_data=(X_val, y_val),
        callbacks=callbacks,
        class_weight=class_weight_dict,
        verbose=1
    )
    
    # 保存最终模型
    final_model_path = model_save_path / "enhanced_tactical_classifier.h5"
    model.save(str(final_model_path))
    
    # 保存类别标签
    labels_path = model_save_path / "enhanced_tactical_classifier.labels.npy"
    label_encoder = {name: i for i, name in enumerate(class_names)}
    np.save(str(labels_path), label_encoder)
    
    logger.info(f"增强分类器训练完成并保存到: {final_model_path}")
    
    # 详细评估
    test_loss, test_acc, test_top_k = model.evaluate(X_val, y_val, verbose=0)
    logger.info(f"验证集性能:")
    logger.info(f"  - Loss: {test_loss:.4f}")
    logger.info(f"  - Accuracy: {test_acc:.4f}")
    logger.info(f"  - Top-K Accuracy: {test_top_k:.4f}")
    
    # 测试每个类别的预测
    logger.info("各类别预测测试:")
    test_samples = X_val[:40]  # 每类约5个样本
    predictions = model.predict(test_samples)
    
    class_correct = {name: 0 for name in class_names}
    class_total = {name: 0 for name in class_names}
    
    for i, pred in enumerate(predictions):
        predicted_class = np.argmax(pred)
        actual_class = np.argmax(y_val[i])
        
        actual_name = class_names[actual_class]
        predicted_name = class_names[predicted_class]
        
        class_total[actual_name] += 1
        if predicted_class == actual_class:
            class_correct[actual_name] += 1
        
        if i < 8:  # 显示前8个预测
            confidence = pred[predicted_class]
            logger.info(f"  样本{i+1}: 预测={predicted_name} (置信度:{confidence:.3f}), 实际={actual_name}")
    
    # 显示各类别准确率
    logger.info("各类别准确率:")
    for name in class_names:
        if class_total[name] > 0:
            accuracy = class_correct[name] / class_total[name]
            logger.info(f"  {name}: {accuracy:.3f} ({class_correct[name]}/{class_total[name]})")


if __name__ == "__main__":
    main()
