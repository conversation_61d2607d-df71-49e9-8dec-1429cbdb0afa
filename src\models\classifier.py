"""
战术模式分类器
基于Softmax的战术行为识别分类器
"""

import numpy as np
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
from typing import List, Optional, Dict
from pathlib import Path

from config.model_config import ClassifierConfig
from config.logging_config import default_logger as logger


class TacticalClassifier:
    """战术模式分类器"""
    
    def __init__(self, config: Optional[ClassifierConfig] = None):
        """
        初始化分类器
        
        Args:
            config: 分类器配置参数
        """
        self.config = config or ClassifierConfig()
        self.model: Optional[keras.Model] = None
        self.is_trained = False
        self.label_encoder = {}
        self.reverse_label_encoder = {}
        
        # 初始化标签编码器
        self._setup_label_encoder()
    
    def _setup_label_encoder(self):
        """设置标签编码器"""
        for i, mode in enumerate(self.config.tactical_modes):
            self.label_encoder[mode] = i
            self.reverse_label_encoder[i] = mode
    
    def build_model(self, input_dim: int) -> keras.Model:
        """
        构建分类器模型
        
        Args:
            input_dim: 输入特征维度
            
        Returns:
            构建的模型
        """
        logger.info("构建战术模式分类器...")
        
        # 输入层
        inputs = keras.Input(shape=(input_dim,), name="tactical_features")
        
        # 隐藏层
        x = inputs
        for i, hidden_size in enumerate(self.config.hidden_layers):
            x = layers.Dense(
                hidden_size,
                activation=self.config.activation,
                name=f"hidden_{i+1}"
            )(x)
            x = layers.Dropout(self.config.dropout_rate)(x)
        
        # 输出层 - Softmax分类
        outputs = layers.Dense(
            self.config.num_classes,
            activation="softmax",
            name="tactical_output"
        )(x)
        
        # 创建模型
        model = keras.Model(inputs=inputs, outputs=outputs, name="Tactical_Classifier")
        
        # 编译模型
        optimizer = keras.optimizers.Adam(learning_rate=self.config.learning_rate)
        model.compile(
            optimizer=optimizer,
            loss="categorical_crossentropy",
            metrics=["accuracy", "top_k_categorical_accuracy"]
        )
        
        self.model = model
        logger.info(f"分类器构建完成，参数量: {model.count_params()}")
        
        return model
    
    def encode_labels(self, labels: List[str]) -> np.ndarray:
        """编码标签为one-hot向量"""
        encoded = np.zeros((len(labels), self.config.num_classes))
        for i, label in enumerate(labels):
            if label in self.label_encoder:
                encoded[i, self.label_encoder[label]] = 1
            else:
                # 未知标签归类为"其他"
                encoded[i, self.label_encoder["其他"]] = 1
        return encoded
    
    def decode_predictions(self, predictions: np.ndarray) -> List[Dict]:
        """
        解码预测结果
        
        Args:
            predictions: 模型预测概率 (batch_size, num_classes)
            
        Returns:
            解码后的预测结果列表
        """
        results = []
        for pred in predictions:
            # 获取最高概率的类别
            predicted_class = np.argmax(pred)
            confidence = float(pred[predicted_class])
            
            # 获取所有类别的概率
            class_probabilities = {}
            for class_idx, prob in enumerate(pred):
                class_name = self.reverse_label_encoder[class_idx]
                class_probabilities[class_name] = float(prob)
            
            result = {
                "predicted_mode": self.reverse_label_encoder[predicted_class],
                "confidence": confidence,
                "probabilities": class_probabilities
            }
            results.append(result)
        
        return results
    
    def train(self, X_train: np.ndarray, y_train: List[str],
              X_val: Optional[np.ndarray] = None, y_val: Optional[List[str]] = None) -> keras.callbacks.History:
        """
        训练分类器
        
        Args:
            X_train: 训练特征数据
            y_train: 训练标签
            X_val: 验证特征数据
            y_val: 验证标签
            
        Returns:
            训练历史
        """
        if self.model is None:
            self.build_model(X_train.shape[1])
        
        logger.info("开始训练战术模式分类器...")
        
        # 编码标签
        y_train_encoded = self.encode_labels(y_train)
        y_val_encoded = None
        if y_val is not None:
            y_val_encoded = self.encode_labels(y_val)
        
        # 设置回调函数
        callbacks = [
            keras.callbacks.EarlyStopping(
                monitor="val_accuracy",
                patience=10,
                restore_best_weights=True
            ),
            keras.callbacks.ReduceLROnPlateau(
                monitor="val_loss",
                factor=0.5,
                patience=5,
                min_lr=1e-6
            )
        ]
        
        # 准备验证数据
        validation_data = None
        if X_val is not None and y_val_encoded is not None:
            validation_data = (X_val, y_val_encoded)
        
        # 训练模型
        history = self.model.fit(
            X_train, y_train_encoded,
            batch_size=self.config.batch_size,
            epochs=self.config.epochs,
            validation_data=validation_data,
            callbacks=callbacks,
            verbose=1
        )
        
        self.is_trained = True
        logger.info("战术模式分类器训练完成")
        
        return history
    
    def predict(self, X: np.ndarray) -> List[Dict]:
        """
        预测战术模式
        
        Args:
            X: 输入特征数据
            
        Returns:
            预测结果列表
        """
        if self.model is None:
            raise ValueError("模型未构建，请先调用build_model()或load_model()")
        
        predictions = self.model.predict(X)
        return self.decode_predictions(predictions)
    
    def predict_single(self, features: np.ndarray) -> Dict:
        """预测单个样本的战术模式"""
        if features.ndim == 1:
            features = features.reshape(1, -1)
        
        results = self.predict(features)
        return results[0]
    
    def save_model(self, filepath: str):
        """保存模型"""
        if self.model is None:
            raise ValueError("模型未构建，无法保存")
        
        Path(filepath).parent.mkdir(parents=True, exist_ok=True)
        self.model.save(filepath)
        
        # 保存标签编码器
        label_encoder_path = str(Path(filepath).with_suffix('.labels.npy'))
        np.save(label_encoder_path, self.label_encoder)
        
        logger.info(f"分类器模型已保存到: {filepath}")
    
    def load_model(self, filepath: str):
        """加载模型"""
        if not Path(filepath).exists():
            raise FileNotFoundError(f"模型文件不存在: {filepath}")
        
        self.model = keras.models.load_model(filepath)
        
        # 加载标签编码器
        label_encoder_path = str(Path(filepath).with_suffix('.labels.npy'))
        if Path(label_encoder_path).exists():
            self.label_encoder = np.load(label_encoder_path, allow_pickle=True).item()
            self.reverse_label_encoder = {v: k for k, v in self.label_encoder.items()}
        
        self.is_trained = True
        logger.info(f"分类器模型已从 {filepath} 加载")
    
    def evaluate(self, X_test: np.ndarray, y_test: List[str]) -> Dict:
        """评估分类器性能"""
        if self.model is None:
            raise ValueError("模型未构建，无法评估")
        
        y_test_encoded = self.encode_labels(y_test)
        results = self.model.evaluate(X_test, y_test_encoded, verbose=0)
        
        metrics = {}
        for i, metric_name in enumerate(self.model.metrics_names):
            metrics[metric_name] = results[i]
        
        logger.info(f"分类器评估结果: {metrics}")
        return metrics
