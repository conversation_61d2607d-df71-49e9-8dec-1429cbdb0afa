"""
多站点协同观测演示
验证多侦查平台的TOA/PA数据融合和预警机探测模式识别
"""

import sys
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
import matplotlib.pyplot as plt

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.multi_station_fusion import MultiStationDataFusion, AWACSTrajectoryPredictor, create_multi_station_demo

# 设置日志
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False


def test_multi_station_fusion():
    """测试多站点数据融合"""
    logger.info("=== 多站点数据融合测试 ===")
    
    # 创建演示场景
    fusion_engine = create_multi_station_demo()
    
    # 创建预测器
    predictor = AWACSTrajectoryPredictor(fusion_engine)
    
    # 预测预警机状态
    result = predictor.predict_awacs_state("awacs_001")
    
    if "error" in result:
        logger.error(f"预测失败: {result['error']}")
        return
    
    # 输出结果
    logger.info("🎯 预警机状态预测结果:")
    logger.info(f"  📡 目标ID: {result.get('target_id')}")
    logger.info(f"  🔍 预测探测模式: {result.get('predicted_mode')}")
    logger.info(f"  📊 模式概率分布:")
    
    mode_probs = result.get('detection_mode_probabilities', {})
    for mode, prob in mode_probs.items():
        logger.info(f"    - {mode}: {prob:.3f}")
    
    logger.info(f"  🎯 融合置信度: {result.get('fusion_confidence', 0):.3f}")
    logger.info(f"  📍 参与站点数: {result.get('station_count')}")
    logger.info(f"  📈 时序样本数: {result.get('sample_count')}")
    
    return result


def visualize_multi_station_data():
    """可视化多站点数据"""
    logger.info("=== 多站点数据可视化 ===")
    
    # 创建演示场景
    fusion_engine = create_multi_station_demo()
    
    # 获取融合航迹
    fused_trajectory = fusion_engine.create_fused_trajectory("awacs_001")
    
    if not fused_trajectory:
        logger.error("无法创建融合航迹")
        return
    
    # 创建可视化
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 1. TOA时序图
    toa_values = [signal.toa for signal in fused_trajectory.signals]
    time_steps = range(len(toa_values))
    
    axes[0, 0].plot(time_steps, toa_values, 'b-', linewidth=2, marker='o')
    axes[0, 0].set_title('TOA (到达时间) 时序变化')
    axes[0, 0].set_xlabel('时间步')
    axes[0, 0].set_ylabel('TOA (秒)')
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. PA时序图
    pa_values = [signal.pa for signal in fused_trajectory.signals]
    
    axes[0, 1].plot(time_steps, pa_values, 'r-', linewidth=2, marker='s')
    axes[0, 1].set_title('PA (相位角) 时序变化')
    axes[0, 1].set_xlabel('时间步')
    axes[0, 1].set_ylabel('PA (弧度)')
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. TOA-PA散点图
    axes[1, 0].scatter(toa_values, pa_values, c=time_steps, cmap='viridis', s=50)
    axes[1, 0].set_title('TOA-PA关系图')
    axes[1, 0].set_xlabel('TOA (秒)')
    axes[1, 0].set_ylabel('PA (弧度)')
    axes[1, 0].grid(True, alpha=0.3)
    
    # 添加颜色条
    cbar = plt.colorbar(axes[1, 0].collections[0], ax=axes[1, 0])
    cbar.set_label('时间步')
    
    # 4. 信号置信度
    confidences = [signal.confidence for signal in fused_trajectory.signals]
    
    axes[1, 1].plot(time_steps, confidences, 'g-', linewidth=2, marker='^')
    axes[1, 1].set_title('融合信号置信度')
    axes[1, 1].set_xlabel('时间步')
    axes[1, 1].set_ylabel('置信度')
    axes[1, 1].grid(True, alpha=0.3)
    axes[1, 1].set_ylim(0, 1)
    
    plt.tight_layout()
    plt.savefig('multi_station_fusion_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    logger.info("多站点数据可视化完成，保存为: multi_station_fusion_analysis.png")


def demonstrate_detection_modes():
    """演示三种探测模式的识别"""
    logger.info("=== 预警机探测模式识别演示 ===")
    
    from src.data.multi_station_fusion import AWACSDetectionModeClassifier
    
    classifier = AWACSDetectionModeClassifier()
    
    # 模拟三种探测模式的TOA/PA数据
    modes_data = {}
    
    # 1. 机械扫描：规律变化
    mechanical_toa = [0.1 + i * 0.005 + np.random.normal(0, 0.0005) for i in range(20)]
    mechanical_pa = [np.pi/3 + i * 0.01 + np.random.normal(0, 0.002) for i in range(20)]
    modes_data["机械扫描"] = np.array([mechanical_toa, mechanical_pa]).T
    
    # 2. 扇面扫描：PA变化大，TOA相对稳定
    sector_toa = [0.15 + np.random.normal(0, 0.001) for i in range(20)]
    sector_pa = [np.pi/4 + 0.5 * np.sin(i * 0.3) + np.random.normal(0, 0.01) for i in range(20)]
    modes_data["扇面扫描"] = np.array([sector_toa, sector_pa]).T
    
    # 3. 引导拦截：TOA和PA都有较大变化
    intercept_toa = [0.12 + 0.02 * np.sin(i * 0.4) + np.random.normal(0, 0.002) for i in range(20)]
    intercept_pa = [np.pi/2 + 0.3 * np.cos(i * 0.5) + np.random.normal(0, 0.015) for i in range(20)]
    modes_data["引导拦截"] = np.array([intercept_toa, intercept_pa]).T
    
    # 可视化三种模式
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    colors = ['blue', 'red', 'green']
    
    # 1. TOA对比
    for i, (mode, data) in enumerate(modes_data.items()):
        axes[0, 0].plot(data[:, 0], color=colors[i], label=mode, linewidth=2, marker='o')
    
    axes[0, 0].set_title('三种探测模式的TOA对比')
    axes[0, 0].set_xlabel('时间步')
    axes[0, 0].set_ylabel('TOA (秒)')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. PA对比
    for i, (mode, data) in enumerate(modes_data.items()):
        axes[0, 1].plot(data[:, 1], color=colors[i], label=mode, linewidth=2, marker='s')
    
    axes[0, 1].set_title('三种探测模式的PA对比')
    axes[0, 1].set_xlabel('时间步')
    axes[0, 1].set_ylabel('PA (弧度)')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. TOA-PA散点图
    for i, (mode, data) in enumerate(modes_data.items()):
        axes[1, 0].scatter(data[:, 0], data[:, 1], color=colors[i], label=mode, s=50, alpha=0.7)
    
    axes[1, 0].set_title('TOA-PA关系图')
    axes[1, 0].set_xlabel('TOA (秒)')
    axes[1, 0].set_ylabel('PA (弧度)')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 4. 分类结果
    classification_results = {}
    for mode, data in modes_data.items():
        features = classifier.extract_detection_features([data])
        probabilities = classifier.classify_detection_mode(features)
        classification_results[mode] = probabilities
    
    # 绘制分类结果热力图
    import pandas as pd
    import seaborn as sns
    
    df_results = pd.DataFrame(classification_results).T
    sns.heatmap(df_results, annot=True, fmt='.3f', cmap='RdYlBu_r', ax=axes[1, 1])
    axes[1, 1].set_title('探测模式分类结果')
    axes[1, 1].set_xlabel('预测模式')
    axes[1, 1].set_ylabel('真实模式')
    
    plt.tight_layout()
    plt.savefig('detection_modes_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    logger.info("探测模式识别演示完成，保存为: detection_modes_analysis.png")
    
    # 输出分类结果
    logger.info("🎯 探测模式分类结果:")
    for true_mode, predictions in classification_results.items():
        predicted_mode = max(predictions.items(), key=lambda x: x[1])[0]
        confidence = predictions[predicted_mode]
        logger.info(f"  {true_mode} -> {predicted_mode} (置信度: {confidence:.3f})")


def main():
    """主演示函数"""
    logger.info("🚀 多站点协同观测演示开始...")
    logger.info("=" * 60)
    
    # 1. 测试多站点数据融合
    fusion_result = test_multi_station_fusion()
    
    # 2. 可视化多站点数据
    visualize_multi_station_data()
    
    # 3. 演示探测模式识别
    demonstrate_detection_modes()
    
    logger.info("=" * 60)
    logger.info("🎉 多站点协同观测演示完成！")
    logger.info("\n📁 生成的可视化文件:")
    logger.info("  📊 multi_station_fusion_analysis.png - 多站点融合分析")
    logger.info("  🎯 detection_modes_analysis.png - 探测模式识别分析")
    
    logger.info("\n💡 演示说明:")
    logger.info("  • 多站点TOA/PA数据时序对齐和融合")
    logger.info("  • 预警机三种探测模式的特征差异")
    logger.info("  • 基于LSTM的预警机状态预测")
    logger.info("  • 符合图11所示的多站点数据融合架构")


if __name__ == "__main__":
    main()
