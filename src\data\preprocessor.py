"""
数据预处理模块
负责数据清洗、特征提取和时间序列构建
"""

import numpy as np
from typing import List, Tuple, Optional
from sklearn.preprocessing import MinMaxScaler, StandardScaler

from .radar_data import TargetTrajectory, RadarSignal
from config.logging_config import default_logger as logger


class DataPreprocessor:
    """数据预处理器"""
    
    def __init__(self, normalization_method: str = "minmax"):
        """
        初始化预处理器
        
        Args:
            normalization_method: 归一化方法 ("minmax" 或 "standard")
        """
        self.normalization_method = normalization_method
        self.scaler = None
        self.is_fitted = False
        
        if normalization_method == "minmax":
            self.scaler = MinMaxScaler()
        elif normalization_method == "standard":
            self.scaler = StandardScaler()
        else:
            raise ValueError(f"不支持的归一化方法: {normalization_method}")
    
    def extract_features(self, trajectory: TargetTrajectory) -> np.ndarray:
        """
        从航迹中提取增强特征（22维）

        Args:
            trajectory: 目标航迹

        Returns:
            特征矩阵 (time_steps, 22_features)
        """
        if not trajectory.signals:
            return np.array([])

        # 按时间排序
        sorted_signals = sorted(trajectory.signals, key=lambda x: x.timestamp)

        features = []
        for signal in sorted_signals:
            # 使用增强的特征向量
            feature_vector = signal.to_feature_vector()
            features.append(feature_vector)

        return np.array(features)

    def extract_tactical_features(self, trajectory: TargetTrajectory) -> np.ndarray:
        """
        提取战术分析特征（用于分类器）

        Args:
            trajectory: 目标航迹

        Returns:
            战术特征向量 (15维)
        """
        if not trajectory.signals or len(trajectory.signals) < 5:
            return np.zeros(15)

        signals = sorted(trajectory.signals, key=lambda x: x.timestamp)

        # 位置和运动统计
        positions = np.array([s.position for s in signals])
        velocities = np.array([s.velocity for s in signals])
        accelerations = np.array([s.acceleration for s in signals])

        # 1. 运动特征 (6维)
        speed_values = [np.linalg.norm(v) for v in velocities]
        avg_speed = np.mean(speed_values)
        speed_std = np.std(speed_values)
        max_speed = np.max(speed_values)

        accel_magnitudes = [np.linalg.norm(a) for a in accelerations]
        avg_acceleration = np.mean(accel_magnitudes)
        max_acceleration = np.max(accel_magnitudes)

        # 计算机动性指标
        maneuverability = np.std(speed_values) + np.mean(accel_magnitudes)

        # 2. 高度特征 (3维)
        altitudes = positions[:, 2]
        avg_altitude = np.mean(altitudes)
        altitude_change = np.max(altitudes) - np.min(altitudes)
        altitude_trend = (altitudes[-1] - altitudes[0]) / len(altitudes)  # 高度变化趋势

        # 3. 姿态特征 (3维)
        headings = [s.heading for s in signals]
        heading_changes = np.abs(np.diff(headings))
        avg_heading_change = np.mean(heading_changes) if len(heading_changes) > 0 else 0

        pitches = [s.pitch for s in signals]
        rolls = [s.roll for s in signals]
        attitude_variance = np.std(pitches) + np.std(rolls)

        # 4. 信号特征 (3维)
        avg_signal_strength = np.mean([s.signal_strength for s in signals])
        communication_disruption = np.mean([s.communication_status for s in signals])
        signal_quality = np.mean([s.snr for s in signals])

        return np.array([
            avg_speed, speed_std, max_speed, avg_acceleration, max_acceleration, maneuverability,
            avg_altitude, altitude_change, altitude_trend,
            avg_heading_change, attitude_variance, 0,  # 预留一个特征位
            avg_signal_strength, communication_disruption, signal_quality
        ])
    
    def create_sequences(self, features: np.ndarray, 
                        sequence_length: int, 
                        prediction_horizon: int) -> Tuple[np.ndarray, np.ndarray]:
        """
        创建时间序列训练数据
        
        Args:
            features: 特征数据 (time_steps, feature_dim)
            sequence_length: 输入序列长度
            prediction_horizon: 预测时间步长
            
        Returns:
            (X, y) 训练数据对
        """
        if len(features) < sequence_length + prediction_horizon:
            logger.warning(f"数据长度不足，需要至少 {sequence_length + prediction_horizon} 个点")
            return np.array([]), np.array([])
        
        X, y = [], []
        
        for i in range(len(features) - sequence_length - prediction_horizon + 1):
            # 输入序列
            X.append(features[i:i + sequence_length])
            
            # 预测目标（未来的位置）
            future_positions = features[i + sequence_length:i + sequence_length + prediction_horizon, :3]
            y.append(future_positions)
        
        return np.array(X), np.array(y)
    
    def fit_scaler(self, trajectories: List[TargetTrajectory]):
        """
        拟合归一化器
        
        Args:
            trajectories: 航迹数据列表
        """
        logger.info("拟合数据归一化器...")
        
        all_features = []
        for trajectory in trajectories:
            features = self.extract_features(trajectory)
            if len(features) > 0:
                all_features.append(features)
        
        if not all_features:
            logger.error("没有有效的特征数据用于拟合归一化器")
            return
        
        # 合并所有特征数据
        combined_features = np.vstack(all_features)
        
        # 拟合归一化器
        self.scaler.fit(combined_features)
        self.is_fitted = True
        
        logger.info(f"归一化器拟合完成，特征维度: {combined_features.shape[1]}")
    
    def transform_features(self, features: np.ndarray) -> np.ndarray:
        """
        转换特征数据
        
        Args:
            features: 原始特征数据
            
        Returns:
            归一化后的特征数据
        """
        if not self.is_fitted:
            logger.warning("归一化器未拟合，返回原始数据")
            return features
        
        if len(features) == 0:
            return features
        
        original_shape = features.shape
        
        # 重塑为2D进行归一化
        if features.ndim > 2:
            features_2d = features.reshape(-1, features.shape[-1])
        else:
            features_2d = features
        
        # 应用归一化
        normalized = self.scaler.transform(features_2d)
        
        # 恢复原始形状
        return normalized.reshape(original_shape)
    
    def prepare_training_data(self, trajectories: List[TargetTrajectory],
                            sequence_length: int = 20,
                            prediction_horizon: int = 10) -> Tuple[np.ndarray, np.ndarray]:
        """
        准备训练数据
        
        Args:
            trajectories: 航迹数据列表
            sequence_length: 输入序列长度
            prediction_horizon: 预测时间步长
            
        Returns:
            (X, y) 训练数据
        """
        logger.info(f"准备训练数据，航迹数量: {len(trajectories)}")
        
        # 拟合归一化器
        self.fit_scaler(trajectories)
        
        all_X, all_y = [], []
        
        for trajectory in trajectories:
            # 提取特征
            features = self.extract_features(trajectory)
            if len(features) == 0:
                continue
            
            # 归一化
            normalized_features = self.transform_features(features)
            
            # 创建序列
            X, y = self.create_sequences(normalized_features, sequence_length, prediction_horizon)
            
            if len(X) > 0:
                all_X.append(X)
                all_y.append(y)
        
        if not all_X:
            logger.error("没有有效的训练数据")
            return np.array([]), np.array([])
        
        # 合并所有数据
        X_combined = np.vstack(all_X)
        y_combined = np.vstack(all_y)
        
        logger.info(f"训练数据准备完成，形状: X{X_combined.shape}, y{y_combined.shape}")
        
        return X_combined, y_combined
