"""
增强战术模式分类器
支持8类战术任务识别和高级训练策略
"""

import numpy as np
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
from typing import List, Optional, Dict
from pathlib import Path
from sklearn.utils.class_weight import compute_class_weight

from config.model_config import ClassifierConfig
from config.logging_config import default_logger as logger


class FocalLoss(keras.losses.Loss):
    """Focal Loss实现，用于处理类别不平衡"""
    
    def __init__(self, alpha=1.0, gamma=2.0, label_smoothing=0.0, name="focal_loss"):
        super().__init__(name=name)
        self.alpha = alpha
        self.gamma = gamma
        self.label_smoothing = label_smoothing
    
    def call(self, y_true, y_pred):
        # 标签平滑
        if self.label_smoothing > 0:
            num_classes = tf.cast(tf.shape(y_true)[-1], tf.float32)
            y_true = y_true * (1 - self.label_smoothing) + self.label_smoothing / num_classes
        
        # 计算交叉熵
        ce_loss = tf.keras.losses.categorical_crossentropy(y_true, y_pred)
        
        # 计算概率
        p_t = tf.reduce_sum(y_true * y_pred, axis=-1)
        
        # Focal Loss
        focal_weight = self.alpha * tf.pow(1 - p_t, self.gamma)
        focal_loss = focal_weight * ce_loss
        
        return tf.reduce_mean(focal_loss)


class EnhancedTacticalClassifier:
    """增强战术模式分类器"""
    
    def __init__(self, config: Optional[ClassifierConfig] = None):
        self.config = config or ClassifierConfig()
        self.model: Optional[keras.Model] = None
        self.is_trained = False
        self.label_encoder = {}
        self.reverse_label_encoder = {}
        self.class_weights = None
        self._setup_label_encoder()
    
    def _setup_label_encoder(self):
        """设置标签编码器"""
        for i, mode in enumerate(self.config.tactical_modes):
            self.label_encoder[mode] = i
            self.reverse_label_encoder[i] = mode
    
    def build_model(self, input_dim: int) -> keras.Model:
        """构建增强分类器模型"""
        logger.info("构建增强战术模式分类器...")
        
        inputs = keras.Input(shape=(input_dim,), name="tactical_features")
        
        # 特征预处理层
        x = layers.BatchNormalization(name="input_bn")(inputs)
        
        # 多层感知机
        for i, hidden_size in enumerate(self.config.hidden_layers):
            x = layers.Dense(
                hidden_size,
                activation=self.config.activation,
                kernel_regularizer=keras.regularizers.l2(0.01),
                name=f"hidden_{i+1}"
            )(x)
            
            if self.config.use_batch_norm:
                x = layers.BatchNormalization(name=f"bn_{i+1}")(x)
            
            x = layers.Dropout(self.config.dropout_rate)(x)
        
        # 输出层
        outputs = layers.Dense(
            self.config.num_classes,
            activation="softmax",
            name="tactical_output"
        )(x)
        
        # 创建模型
        model = keras.Model(inputs=inputs, outputs=outputs, name="Enhanced_Tactical_Classifier")
        
        # 选择损失函数
        if self.config.use_focal_loss:
            loss_fn = FocalLoss(
                alpha=1.0,
                gamma=2.0,
                label_smoothing=self.config.label_smoothing
            )
        else:
            loss_fn = keras.losses.CategoricalCrossentropy(
                label_smoothing=self.config.label_smoothing
            )
        
        # 编译模型
        optimizer = keras.optimizers.AdamW(
            learning_rate=self.config.learning_rate,
            weight_decay=0.01
        )
        
        model.compile(
            optimizer=optimizer,
            loss=loss_fn,
            metrics=[
                "accuracy",
                "top_k_categorical_accuracy",
                keras.metrics.Precision(name="precision"),
                keras.metrics.Recall(name="recall"),
                keras.metrics.F1Score(name="f1_score")
            ]
        )
        
        self.model = model
        logger.info(f"增强分类器构建完成，参数量: {model.count_params()}")
        
        return model
    
    def compute_class_weights(self, y_labels: List[str]) -> Dict[int, float]:
        """计算类别权重"""
        if not self.config.use_class_weights:
            return None
        
        # 转换标签为数值
        y_numeric = [self.label_encoder[label] for label in y_labels]
        
        # 计算类别权重
        classes = np.unique(y_numeric)
        weights = compute_class_weight(
            class_weight='balanced',
            classes=classes,
            y=y_numeric
        )
        
        class_weight_dict = {int(cls): float(weight) for cls, weight in zip(classes, weights)}
        
        logger.info(f"计算类别权重: {class_weight_dict}")
        return class_weight_dict
    
    def encode_labels(self, labels: List[str]) -> np.ndarray:
        """编码标签为one-hot向量"""
        encoded = np.zeros((len(labels), self.config.num_classes))
        for i, label in enumerate(labels):
            if label in self.label_encoder:
                encoded[i, self.label_encoder[label]] = 1
            else:
                # 未知标签归类为最后一类
                logger.warning(f"未知战术模式: {label}，归类为其他")
                encoded[i, -1] = 1
        return encoded
    
    def train(self, X_train: np.ndarray, y_train: List[str],
              X_val: Optional[np.ndarray] = None, y_val: Optional[List[str]] = None):
        """训练增强分类器"""
        if self.model is None:
            self.build_model(X_train.shape[1])
        
        logger.info("开始训练增强战术模式分类器...")
        
        # 编码标签
        y_train_encoded = self.encode_labels(y_train)
        y_val_encoded = None
        if y_val is not None:
            y_val_encoded = self.encode_labels(y_val)
        
        # 计算类别权重
        self.class_weights = self.compute_class_weights(y_train)
        
        # 设置回调函数
        callbacks = [
            keras.callbacks.EarlyStopping(
                monitor="val_f1_score",
                patience=15,
                restore_best_weights=True,
                mode="max"
            ),
            keras.callbacks.ReduceLROnPlateau(
                monitor="val_loss",
                factor=0.5,
                patience=8,
                min_lr=1e-7,
                verbose=1
            ),
            keras.callbacks.ModelCheckpoint(
                filepath="data/models/best_classifier.h5",
                monitor="val_f1_score",
                save_best_only=True,
                mode="max",
                verbose=1
            )
        ]
        
        # 准备验证数据
        validation_data = None
        if X_val is not None and y_val_encoded is not None:
            validation_data = (X_val, y_val_encoded)
        
        # 训练模型
        history = self.model.fit(
            X_train, y_train_encoded,
            batch_size=self.config.batch_size,
            epochs=self.config.epochs,
            validation_data=validation_data,
            callbacks=callbacks,
            class_weight=self.class_weights,
            verbose=1
        )
        
        self.is_trained = True
        logger.info("增强战术模式分类器训练完成")
        
        return history
    
    def predict_with_uncertainty(self, X: np.ndarray, num_samples: int = 10) -> List[Dict]:
        """
        使用Monte Carlo Dropout进行不确定性估计的预测
        
        Args:
            X: 输入特征
            num_samples: Monte Carlo采样次数
            
        Returns:
            包含不确定性估计的预测结果
        """
        if self.model is None:
            raise ValueError("模型未构建")
        
        # 启用训练模式进行Monte Carlo Dropout
        predictions = []
        for _ in range(num_samples):
            pred = self.model(X, training=True)
            predictions.append(pred.numpy())
        
        predictions = np.array(predictions)  # (num_samples, batch_size, num_classes)
        
        # 计算统计量
        mean_pred = np.mean(predictions, axis=0)
        std_pred = np.std(predictions, axis=0)
        
        results = []
        for i in range(len(X)):
            predicted_class = np.argmax(mean_pred[i])
            confidence = float(mean_pred[i][predicted_class])
            uncertainty = float(std_pred[i][predicted_class])
            
            class_probabilities = {}
            class_uncertainties = {}
            for class_idx in range(self.config.num_classes):
                class_name = self.reverse_label_encoder[class_idx]
                class_probabilities[class_name] = float(mean_pred[i][class_idx])
                class_uncertainties[class_name] = float(std_pred[i][class_idx])
            
            result = {
                "predicted_mode": self.reverse_label_encoder[predicted_class],
                "confidence": confidence,
                "uncertainty": uncertainty,
                "probabilities": class_probabilities,
                "uncertainties": class_uncertainties
            }
            results.append(result)
        
        return results
