"""
简化的增强可视化演示
测试22维特征和8类战术任务的基本功能
"""

import sys
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
import matplotlib.pyplot as plt

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.enhanced_data_generator import EnhancedDataGenerator

# 设置日志
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False


def test_22d_features():
    """测试22维特征提取"""
    logger.info("=== 测试22维特征提取 ===")
    
    try:
        # 创建数据生成器
        generator = EnhancedDataGenerator()
        
        # 生成一个示例航迹
        trajectory = generator.generate_tactical_trajectory(
            tactical_mode="空中格斗",
            sequence_length=10,
            target_id="test_target"
        )
        
        logger.info(f"生成航迹包含 {len(trajectory.signals)} 个信号点")
        
        # 提取第一个信号的22维特征
        if trajectory.signals:
            signal = trajectory.signals[0]
            feature_vector = signal.to_feature_vector()
            
            logger.info(f"22维特征向量长度: {len(feature_vector)}")
            logger.info(f"特征向量前10维: {feature_vector[:10]}")
            
            # 验证特征维度
            if len(feature_vector) == 27:  # 实际是27维
                logger.info("✅ 特征维度正确")
                
                # 创建简单的特征可视化
                plt.figure(figsize=(12, 8))
                
                # 特征分组可视化
                feature_names = [
                    'pos_x', 'pos_y', 'pos_z', 'pos_acc_x', 'pos_acc_y', 'pos_acc_z',
                    'vel_x', 'vel_y', 'vel_z', 'acc_x', 'acc_y', 'acc_z',
                    'heading', 'elevation', 'pitch', 'roll', 'yaw',
                    'distance', 'pulse_width', 'frequency', 'prf', 'signal_strength', 'comm_status',
                    'toa', 'pa', 'snr', 'confidence'
                ]
                
                plt.subplot(2, 1, 1)
                plt.bar(range(len(feature_vector)), feature_vector)
                plt.title('单个信号的27维特征向量')
                plt.xlabel('特征维度')
                plt.ylabel('特征值')
                plt.xticks(range(len(feature_vector)), feature_names, rotation=45)
                
                # 特征分组统计
                plt.subplot(2, 1, 2)
                groups = {
                    '位置信息': feature_vector[:6],
                    '运动参数': feature_vector[6:12],
                    '姿态信息': feature_vector[12:17],
                    '雷达参数': feature_vector[17:20],
                    '信号特征': feature_vector[20:23],
                    '质量指标': feature_vector[23:27]
                }
                
                group_means = [np.mean(np.abs(values)) for values in groups.values()]
                plt.bar(groups.keys(), group_means, color=['blue', 'green', 'red', 'orange', 'purple', 'brown'])
                plt.title('特征分组平均值')
                plt.ylabel('平均绝对值')
                plt.xticks(rotation=45)
                
                plt.tight_layout()
                plt.savefig('feature_test.png', dpi=300, bbox_inches='tight')
                plt.close()  # 关闭图形，不显示

                logger.info("特征测试完成，保存为: feature_test.png")
                
            else:
                logger.error(f"❌ 特征维度错误，期望27维，实际{len(feature_vector)}维")
        
    except Exception as e:
        logger.error(f"22维特征测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_8class_generation():
    """测试8类战术任务数据生成"""
    logger.info("=== 测试8类战术任务数据生成 ===")
    
    try:
        generator = EnhancedDataGenerator()
        tactical_modes = generator.tactical_modes
        
        logger.info(f"支持的战术模式: {tactical_modes}")
        
        # 为每类生成一个样本
        trajectories = []
        for mode in tactical_modes:
            trajectory = generator.generate_tactical_trajectory(
                tactical_mode=mode,
                sequence_length=5,  # 短序列用于快速测试
                target_id=f"test_{mode}"
            )
            trajectories.append(trajectory)
            logger.info(f"✅ 成功生成 {mode} 航迹，包含 {len(trajectory.signals)} 个信号点")
        
        # 简单的战术模式分布可视化
        plt.figure(figsize=(10, 6))
        mode_counts = [1] * len(tactical_modes)  # 每类1个样本
        
        plt.bar(tactical_modes, mode_counts, color='lightblue')
        plt.title('8类战术任务数据生成测试')
        plt.xlabel('战术任务类型')
        plt.ylabel('样本数量')
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        plt.savefig('8class_generation_test.png', dpi=300, bbox_inches='tight')
        plt.close()  # 关闭图形，不显示

        logger.info("8类战术任务生成测试完成，保存为: 8class_generation_test.png")
        
    except Exception as e:
        logger.error(f"8类战术任务测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_trajectory_visualization():
    """测试航迹可视化"""
    logger.info("=== 测试航迹可视化 ===")
    
    try:
        generator = EnhancedDataGenerator()
        
        # 生成3个不同战术模式的航迹
        modes = ["巡逻任务", "空中格斗", "电子干扰"]
        colors = ['blue', 'red', 'green']
        
        plt.figure(figsize=(12, 8))
        
        for i, mode in enumerate(modes):
            trajectory = generator.generate_tactical_trajectory(
                tactical_mode=mode,
                sequence_length=20,
                target_id=f"target_{i}"
            )
            
            positions = trajectory.get_positions()
            positions_array = np.array(positions)
            
            # 2D轨迹图
            plt.subplot(2, 2, 1)
            plt.plot(positions_array[:, 0], positions_array[:, 1], 
                    color=colors[i], label=mode, linewidth=2)
            plt.scatter(positions_array[0, 0], positions_array[0, 1], 
                       color=colors[i], s=100, marker='o')  # 起点
            plt.scatter(positions_array[-1, 0], positions_array[-1, 1], 
                       color=colors[i], s=100, marker='s')  # 终点
        
        plt.title('2D航迹对比')
        plt.xlabel('X坐标 (m)')
        plt.ylabel('Y坐标 (m)')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 高度变化
        plt.subplot(2, 2, 2)
        for i, mode in enumerate(modes):
            trajectory = generator.generate_tactical_trajectory(
                tactical_mode=mode,
                sequence_length=20,
                target_id=f"target_{i}"
            )
            positions = trajectory.get_positions()
            altitudes = [pos[2] for pos in positions]
            plt.plot(altitudes, color=colors[i], label=mode, linewidth=2)
        
        plt.title('高度变化对比')
        plt.xlabel('时间步')
        plt.ylabel('高度 (m)')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 速度变化
        plt.subplot(2, 2, 3)
        for i, mode in enumerate(modes):
            trajectory = generator.generate_tactical_trajectory(
                tactical_mode=mode,
                sequence_length=20,
                target_id=f"target_{i}"
            )
            velocities = trajectory.get_velocities()
            speeds = [np.linalg.norm(vel) for vel in velocities]
            plt.plot(speeds, color=colors[i], label=mode, linewidth=2)
        
        plt.title('速度变化对比')
        plt.xlabel('时间步')
        plt.ylabel('速度 (m/s)')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 战术模式分布
        plt.subplot(2, 2, 4)
        plt.pie([1, 1, 1], labels=modes, colors=colors, autopct='%1.1f%%')
        plt.title('战术模式分布')
        
        plt.tight_layout()
        plt.savefig('trajectory_test.png', dpi=300, bbox_inches='tight')
        plt.close()  # 关闭图形，不显示

        logger.info("航迹可视化测试完成，保存为: trajectory_test.png")
        
    except Exception as e:
        logger.error(f"航迹可视化测试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主测试函数"""
    logger.info("🚀 简化增强可视化演示开始...")
    logger.info("=" * 50)
    
    # 1. 测试22维特征提取
    test_22d_features()
    
    # 2. 测试8类战术任务生成
    test_8class_generation()
    
    # 3. 测试航迹可视化
    test_trajectory_visualization()
    
    logger.info("=" * 50)
    logger.info("🎉 简化增强可视化演示完成！")
    logger.info("\n📁 生成的测试文件:")
    logger.info("  📊 feature_test.png - 22维特征测试")
    logger.info("  🎯 8class_generation_test.png - 8类任务生成测试")
    logger.info("  🛩️ trajectory_test.png - 航迹可视化测试")


if __name__ == "__main__":
    main()
