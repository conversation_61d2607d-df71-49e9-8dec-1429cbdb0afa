# 部署指南

## 环境要求

### 系统要求
- **操作系统**: Windows 10/11, Linux, macOS
- **Python版本**: 3.8 或更高版本
- **内存**: 最少 8GB RAM（推荐 16GB）
- **存储**: 最少 5GB 可用空间

### 硬件要求
- **CPU**: 多核处理器（推荐 8核以上）
- **GPU**: NVIDIA GPU（可选，用于加速训练）
  - CUDA 11.2 或更高版本
  - 最少 4GB 显存

## 安装步骤

### 1. 克隆项目
```bash
git clone <repository-url>
cd AirModel_V0
```

### 2. 创建虚拟环境
```bash
# 使用 venv
python -m venv airmodel_env
source airmodel_env/bin/activate  # Linux/macOS
# 或
airmodel_env\Scripts\activate     # Windows

# 使用 conda
conda create -n airmodel python=3.9
conda activate airmodel
```

### 3. 安装依赖
```bash
pip install -r requirements.txt
```

### 4. 环境配置
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
nano .env  # 或使用其他编辑器
```

### 5. 训练模型
```bash
# 训练LSTM模型
python scripts/simple_train.py

# 训练分类器
python scripts/train_classifier.py
```

### 6. 验证安装
```bash
# 运行核心功能测试
python scripts/test_core_functions.py

# 运行基本演示
python examples/simple_demo.py
```

## 生产部署

### 使用 Docker（推荐）

1. **创建 Dockerfile**:
```dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["python", "src/api/rest_api.py"]
```

2. **构建镜像**:
```bash
docker build -t airmodel-api .
```

3. **运行容器**:
```bash
docker run -p 8000:8000 -v $(pwd)/data:/app/data airmodel-api
```

### 使用 systemd（Linux）

1. **创建服务文件** `/etc/systemd/system/airmodel.service`:
```ini
[Unit]
Description=AirModel Trajectory Prediction Service
After=network.target

[Service]
Type=simple
User=airmodel
WorkingDirectory=/opt/airmodel
Environment=PATH=/opt/airmodel/venv/bin
ExecStart=/opt/airmodel/venv/bin/python src/api/rest_api.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

2. **启动服务**:
```bash
sudo systemctl enable airmodel
sudo systemctl start airmodel
sudo systemctl status airmodel
```

### 使用 Nginx 反向代理

**Nginx 配置示例**:
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /ws {
        proxy_pass http://127.0.0.1:8001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

## 性能优化

### 1. GPU 加速
```bash
# 安装 GPU 版本的 TensorFlow
pip install tensorflow-gpu

# 设置 GPU 内存增长
export TF_FORCE_GPU_ALLOW_GROWTH=true
```

### 2. 模型优化
```python
# 模型量化
import tensorflow as tf

converter = tf.lite.TFLiteConverter.from_saved_model("data/models/lstm_model")
converter.optimizations = [tf.lite.Optimize.DEFAULT]
tflite_model = converter.convert()
```

### 3. 缓存配置
```bash
# Redis 缓存配置
redis-server --maxmemory 2gb --maxmemory-policy allkeys-lru
```

## 监控和日志

### 1. 日志配置
- 日志文件位置: `logs/airmodel.log`
- 日志级别: INFO（生产环境）
- 日志轮转: 10MB 自动轮转

### 2. 性能监控
```bash
# 安装监控工具
pip install prometheus-client grafana-api

# 启动监控
python scripts/monitoring.py
```

### 3. 健康检查
```bash
# API 健康检查
curl http://localhost:8000/health

# 系统资源监控
htop
nvidia-smi  # GPU 监控
```

## 故障排除

### 常见问题

1. **模型加载失败**
   - 检查模型文件是否存在
   - 验证 TensorFlow 版本兼容性

2. **内存不足**
   - 减少批处理大小
   - 启用模型量化

3. **预测延迟过高**
   - 启用 GPU 加速
   - 优化数据预处理流程

4. **API 连接失败**
   - 检查端口是否被占用
   - 验证防火墙设置

### 日志分析
```bash
# 查看错误日志
grep "ERROR" logs/airmodel.log

# 查看性能日志
grep "performance" logs/airmodel.log

# 实时监控日志
tail -f logs/airmodel.log
```

## 备份和恢复

### 数据备份
```bash
# 备份训练好的模型
tar -czf models_backup_$(date +%Y%m%d).tar.gz data/models/

# 备份配置文件
cp .env config_backup.env
```

### 恢复流程
```bash
# 恢复模型
tar -xzf models_backup_20250903.tar.gz

# 恢复配置
cp config_backup.env .env

# 重启服务
systemctl restart airmodel
```
