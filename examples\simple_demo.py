"""
简化的使用示例
演示如何使用训练好的LSTM模型进行航迹预测
"""

import sys
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import tensorflow as tf
from tensorflow import keras

# 设置日志
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def load_trained_model(model_path: str):
    """加载训练好的模型"""
    if not Path(model_path).exists():
        logger.error(f"模型文件不存在: {model_path}")
        return None
    
    logger.info(f"加载模型: {model_path}")
    model = keras.models.load_model(model_path)
    logger.info("模型加载成功")
    return model


def generate_test_trajectory(sequence_length=20):
    """生成测试航迹数据"""
    logger.info("生成测试航迹数据...")
    
    # 模拟一个真实的航迹：战斗机巡逻轨迹
    trajectory = np.zeros((sequence_length, 6))  # 6维特征：位置(x,y,z) + 速度(vx,vy,vz)
    
    # 初始状态
    trajectory[0] = [1000, 2000, 10000, 150, 50, 0]  # 初始位置和速度
    
    # 生成连续的航迹点
    for t in range(1, sequence_length):
        # 位置更新：位置 = 前一位置 + 速度 * 时间间隔
        trajectory[t, :3] = trajectory[t-1, :3] + trajectory[t-1, 3:6] * 1.0
        
        # 速度有小幅变化（模拟真实飞行）
        trajectory[t, 3:6] = trajectory[t-1, 3:6] + np.random.normal(0, 5, 3)
        
        # 添加一些噪声
        trajectory[t] += np.random.normal(0, 10, 6)
    
    return trajectory.reshape(1, sequence_length, 6)  # 添加batch维度


def visualize_prediction(historical_data, prediction):
    """可视化预测结果"""
    logger.info("生成可视化图表...")
    
    # 提取历史轨迹的位置
    hist_positions = historical_data[0, :, :3]  # (sequence_length, 3)
    
    # 提取预测轨迹的位置
    pred_positions = prediction[0, :, :]  # (prediction_horizon, 3)
    
    # 创建3D图
    fig = plt.figure(figsize=(12, 8))
    ax = fig.add_subplot(111, projection='3d')
    
    # 绘制历史轨迹
    ax.plot(hist_positions[:, 0], hist_positions[:, 1], hist_positions[:, 2], 
            'b-o', label='历史轨迹', linewidth=2, markersize=4)
    
    # 绘制预测轨迹
    ax.plot(pred_positions[:, 0], pred_positions[:, 1], pred_positions[:, 2], 
            'r-s', label='预测轨迹', linewidth=2, markersize=6)
    
    # 连接历史和预测轨迹
    ax.plot([hist_positions[-1, 0], pred_positions[0, 0]], 
            [hist_positions[-1, 1], pred_positions[0, 1]], 
            [hist_positions[-1, 2], pred_positions[0, 2]], 
            'g--', linewidth=1, alpha=0.7)
    
    ax.set_xlabel('X 坐标 (m)')
    ax.set_ylabel('Y 坐标 (m)')
    ax.set_zlabel('Z 坐标 (m)')
    ax.set_title('航迹预测可视化')
    ax.legend()
    
    # 保存图片
    plt.savefig('trajectory_prediction_demo.png', dpi=300, bbox_inches='tight')
    logger.info("可视化图表已保存为: trajectory_prediction_demo.png")
    
    plt.show()


def main():
    """主函数"""
    logger.info("开始航迹预测演示...")
    
    # 加载训练好的模型
    model_path = "data/models/lstm_trajectory_model.h5"
    model = load_trained_model(model_path)
    
    if model is None:
        logger.error("无法加载模型，请先运行训练脚本")
        logger.info("运行命令: python scripts/simple_train.py")
        return
    
    # 生成测试数据
    test_trajectory = generate_test_trajectory()
    
    logger.info(f"测试轨迹形状: {test_trajectory.shape}")
    logger.info(f"历史轨迹起点: {test_trajectory[0, 0, :3]}")
    logger.info(f"历史轨迹终点: {test_trajectory[0, -1, :3]}")
    
    # 进行预测
    logger.info("开始航迹预测...")
    prediction = model.predict(test_trajectory)
    
    logger.info(f"预测结果形状: {prediction.shape}")
    logger.info(f"预测轨迹起点: {prediction[0, 0, :]}")
    logger.info(f"预测轨迹终点: {prediction[0, -1, :]}")
    
    # 可视化结果
    try:
        visualize_prediction(test_trajectory, prediction)
    except Exception as e:
        logger.warning(f"可视化失败: {e}")
    
    # 计算预测统计信息
    pred_distances = []
    for i in range(len(prediction[0])):
        if i == 0:
            # 第一个预测点与历史最后一点的距离
            last_hist = test_trajectory[0, -1, :3]
            first_pred = prediction[0, 0, :]
            dist = np.linalg.norm(first_pred - last_hist)
        else:
            # 相邻预测点之间的距离
            dist = np.linalg.norm(prediction[0, i, :] - prediction[0, i-1, :])
        pred_distances.append(dist)
    
    logger.info(f"平均预测步长: {np.mean(pred_distances):.2f} m")
    logger.info(f"最大预测步长: {np.max(pred_distances):.2f} m")
    
    logger.info("航迹预测演示完成！")


if __name__ == "__main__":
    main()
