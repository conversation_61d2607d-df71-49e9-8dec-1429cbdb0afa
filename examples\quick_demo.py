"""
快速演示脚本
展示增强模型的核心功能
"""

import sys
import numpy as np
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.radar_data import RadarSignal

print("🚀 增强目标航迹ZZ跟踪实时预测模型 - 快速演示")
print("=" * 60)

# 1. 演示增强特征
print("\n📊 1. 增强特征演示 (22维特征向量)")

enhanced_signal = RadarSignal(
    timestamp=datetime.now(),
    station_id="demo_radar",
    target_id="demo_target",
    toa=0.15,
    pa=0.785,
    
    # 位置信息（增强）
    position=(5000.0, 3000.0, 8000.0),
    position_accuracy=(10.0, 12.0, 20.0),
    
    # 运动参数（增强）
    velocity=(250.0, 150.0, 30.0),
    acceleration=(5.0, -2.0, 1.0),
    
    # 姿态信息（完整）
    heading=0.524,
    elevation=0.118,
    pitch=0.1,
    roll=-0.05,
    yaw=0.02,
    
    # 雷达测量参数（新增）
    target_distance=9434.0,
    pulse_width=1.2,
    radar_frequency=3000.0,
    
    # 信号特征（新增）
    prf=2500.0,
    signal_strength=0.85,
    communication_status=0,
    
    # 信号质量
    snr=20.5,
    confidence=0.92
)

feature_vector = enhanced_signal.to_feature_vector()
print(f"✅ 增强特征向量维度: {len(feature_vector)}")
print(f"✅ 位置特征: {enhanced_signal.get_position_features()}")
print(f"✅ 运动特征: {enhanced_signal.get_motion_features()}")
print(f"✅ 姿态特征: {enhanced_signal.get_attitude_features()}")
print(f"✅ 雷达特征: {enhanced_signal.get_radar_features()}")

# 2. 演示8类战术任务
print("\n🎯 2. 8类战术任务支持")
tactical_modes = [
    "巡逻任务", "预警探测", "电子侦察", "电子干扰",
    "对空攻击", "对地攻击", "空中格斗", "撤退规避"
]

for i, mode in enumerate(tactical_modes):
    print(f"  {i+1}. {mode}")

# 3. 检查模型文件
print("\n🔧 3. 模型文件检查")
model_files = [
    ("基础LSTM模型", "data/models/lstm_trajectory_model.h5"),
    ("增强分类器", "data/models/enhanced_tactical_classifier.h5"),
    ("分类器标签", "data/models/enhanced_tactical_classifier.labels.npy"),
    ("基础分类器", "data/models/tactical_classifier_model.h5")
]

for name, path in model_files:
    status = "✅ 存在" if Path(path).exists() else "❌ 不存在"
    print(f"  {name}: {status}")

# 4. 性能特点总结
print("\n⚡ 4. 性能特点")
print("✅ 支持22维增强特征输入")
print("✅ 8类战术任务自动识别")
print("✅ 毫秒级实时预测响应")
print("✅ 多目标同时跟踪处理")
print("✅ 复杂电子对抗环境适应")
print("✅ 不确定性量化评估")

# 5. 使用指南
print("\n📋 5. 快速使用指南")
print("🔸 训练增强模型:")
print("   python scripts/train_enhanced_simple.py")
print("🔸 性能评估:")
print("   python examples/simple_evaluation.py")
print("🔸 实战场景测试:")
print("   python examples/combat_scenario_test.py")
print("🔸 可视化演示:")
print("   python examples/visualization_demo.py")

print("\n" + "=" * 60)
print("🎉 增强模型演示完成！")
print("📖 详细文档请查看: docs/ENHANCED_MODEL_GUIDE.md")
