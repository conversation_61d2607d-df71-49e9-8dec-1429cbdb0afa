"""
战术分类器训练脚本
训练基于Softmax的战术模式识别分类器
"""

import sys
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers

# 设置日志
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_classifier_model(input_dim=10, num_classes=6):
    """创建战术分类器模型"""
    logger.info("构建战术模式分类器...")
    
    inputs = keras.Input(shape=(input_dim,), name="tactical_features")
    
    # 隐藏层
    x = layers.Dense(64, activation="relu", name="hidden_1")(inputs)
    x = layers.Dropout(0.3)(x)
    x = layers.Dense(32, activation="relu", name="hidden_2")(x)
    x = layers.Dropout(0.3)(x)
    
    # 输出层 - Softmax分类
    outputs = layers.Dense(num_classes, activation="softmax", name="tactical_output")(x)
    
    model = keras.Model(inputs=inputs, outputs=outputs, name="Tactical_Classifier")
    
    # 编译模型
    optimizer = keras.optimizers.Adam(learning_rate=0.001)
    model.compile(
        optimizer=optimizer,
        loss="categorical_crossentropy",
        metrics=["accuracy", "top_k_categorical_accuracy"]
    )
    
    logger.info(f"分类器构建完成，参数量: {model.count_params()}")
    return model


def generate_tactical_features(num_samples=1000):
    """生成战术特征数据"""
    logger.info("生成战术特征数据...")
    
    tactical_modes = ["巡逻", "预警探测", "攻击突击", "空中格斗", "对抗", "其他"]
    
    X = []
    y = []
    
    for i in range(num_samples):
        # 随机选择战术模式
        mode_idx = np.random.randint(0, len(tactical_modes))
        mode = tactical_modes[mode_idx]
        
        # 根据不同战术模式生成特征
        if mode == "巡逻":
            # 巡逻：速度稳定，高度变化小，航向变化小
            features = [
                np.random.normal(150, 20),    # 平均速度
                np.random.normal(10, 5),      # 速度标准差
                np.random.normal(10000, 200), # 平均高度
                np.random.normal(50, 20),     # 高度标准差
                np.random.normal(0.1, 0.05),  # 航向变化率
                np.random.normal(0.02, 0.01), # 俯仰角变化
                np.random.normal(0.01, 0.005), # 滚转角变化
                np.random.normal(20, 3),      # 信噪比
                np.random.normal(0.9, 0.05),  # 平均置信度
                np.random.normal(300, 50)     # 航迹持续时间
            ]
        elif mode == "攻击突击":
            # 攻击：高速，直线，高度下降
            features = [
                np.random.normal(400, 50),    # 高速度
                np.random.normal(30, 10),     # 速度变化大
                np.random.normal(8000, 500),  # 较低高度
                np.random.normal(200, 50),    # 高度变化大
                np.random.normal(0.05, 0.02), # 航向变化小
                np.random.normal(0.1, 0.03),  # 俯仰角变化大
                np.random.normal(0.05, 0.02), # 滚转角变化
                np.random.normal(18, 4),      # 信噪比
                np.random.normal(0.85, 0.1),  # 置信度
                np.random.normal(120, 30)     # 较短持续时间
            ]
        elif mode == "空中格斗":
            # 格斗：高机动，频繁变向
            features = [
                np.random.normal(300, 80),    # 变化的速度
                np.random.normal(60, 20),     # 速度变化很大
                np.random.normal(7000, 1000), # 中等高度
                np.random.normal(300, 100),   # 高度变化大
                np.random.normal(0.3, 0.1),   # 航向变化大
                np.random.normal(0.2, 0.05),  # 俯仰角变化大
                np.random.normal(0.15, 0.05), # 滚转角变化大
                np.random.normal(15, 5),      # 较低信噪比
                np.random.normal(0.8, 0.1),   # 置信度
                np.random.normal(180, 60)     # 中等持续时间
            ]
        else:
            # 其他模式：随机特征
            features = [
                np.random.normal(200, 100),   # 随机速度
                np.random.normal(40, 20),     # 随机速度变化
                np.random.normal(9000, 2000), # 随机高度
                np.random.normal(150, 100),   # 随机高度变化
                np.random.normal(0.2, 0.1),   # 随机航向变化
                np.random.normal(0.1, 0.05),  # 随机俯仰角变化
                np.random.normal(0.08, 0.04), # 随机滚转角变化
                np.random.normal(17, 6),      # 随机信噪比
                np.random.normal(0.85, 0.1),  # 随机置信度
                np.random.normal(240, 120)    # 随机持续时间
            ]
        
        X.append(features)
        
        # 创建one-hot标签
        label = np.zeros(len(tactical_modes))
        label[mode_idx] = 1
        y.append(label)
    
    return np.array(X), np.array(y), tactical_modes


def main():
    """主训练函数"""
    logger.info("开始战术分类器训练...")
    
    # 创建保存目录
    model_save_path = Path("data/models")
    model_save_path.mkdir(parents=True, exist_ok=True)
    
    # 生成训练数据
    X, y, class_names = generate_tactical_features()
    
    # 数据分割
    split_idx = int(len(X) * 0.7)
    X_train, X_val = X[:split_idx], X[split_idx:]
    y_train, y_val = y[:split_idx], y[split_idx:]
    
    logger.info(f"训练数据: {X_train.shape}, 验证数据: {X_val.shape}")
    logger.info(f"战术模式类别: {class_names}")
    
    # 创建模型
    model = create_classifier_model(input_dim=X.shape[1], num_classes=len(class_names))
    
    # 设置回调函数
    callbacks = [
        keras.callbacks.EarlyStopping(
            monitor="val_accuracy",
            patience=10,
            restore_best_weights=True
        ),
        keras.callbacks.ReduceLROnPlateau(
            monitor="val_loss",
            factor=0.5,
            patience=5,
            min_lr=1e-6
        )
    ]
    
    # 训练模型
    logger.info("开始训练分类器...")
    history = model.fit(
        X_train, y_train,
        batch_size=64,
        epochs=50,
        validation_data=(X_val, y_val),
        callbacks=callbacks,
        verbose=1
    )
    
    # 保存模型
    model_path = model_save_path / "tactical_classifier_model.h5"
    model.save(str(model_path))
    
    # 保存类别标签
    labels_path = model_save_path / "tactical_classifier_model.labels.npy"
    label_encoder = {name: i for i, name in enumerate(class_names)}
    np.save(str(labels_path), label_encoder)
    
    logger.info(f"分类器训练完成并保存到: {model_path}")
    
    # 评估性能
    test_loss, test_acc, test_top_k = model.evaluate(X_val, y_val, verbose=0)
    logger.info(f"验证集性能 - Loss: {test_loss:.4f}, Accuracy: {test_acc:.4f}, Top-K Accuracy: {test_top_k:.4f}")
    
    # 测试预测
    logger.info("测试预测功能...")
    test_sample = X_val[:5]  # 取5个测试样本
    predictions = model.predict(test_sample)
    
    for i, pred in enumerate(predictions):
        predicted_class = np.argmax(pred)
        confidence = pred[predicted_class]
        actual_class = np.argmax(y_val[i])
        
        logger.info(f"样本 {i+1}: 预测={class_names[predicted_class]} (置信度:{confidence:.3f}), "
                   f"实际={class_names[actual_class]}")


if __name__ == "__main__":
    main()
