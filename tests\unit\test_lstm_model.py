"""
LSTM模型单元测试
"""

import sys
import pytest
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.models.lstm_model import LSTMTrajectoryModel
from config.model_config import LSTMConfig


class TestLSTMTrajectoryModel:
    """LSTM航迹预测模型测试类"""
    
    def setup_method(self):
        """测试前设置"""
        self.config = LSTMConfig()
        self.config.epochs = 2  # 减少训练轮数用于测试
        self.model = LSTMTrajectoryModel(self.config)
    
    def test_model_initialization(self):
        """测试模型初始化"""
        assert self.model.config is not None
        assert self.model.model is None
        assert not self.model.is_trained
    
    def test_build_model(self):
        """测试模型构建"""
        model = self.model.build_model()
        
        assert model is not None
        assert self.model.model is not None
        assert model.input_shape == (None, self.config.sequence_length, self.config.input_dim)
        assert model.output_shape == (None, self.config.prediction_horizon, self.config.output_dim)
    
    def test_model_training(self):
        """测试模型训练"""
        # 生成测试数据
        num_samples = 100
        X_train = np.random.randn(num_samples, self.config.sequence_length, self.config.input_dim)
        y_train = np.random.randn(num_samples, self.config.prediction_horizon, self.config.output_dim)
        
        X_val = np.random.randn(30, self.config.sequence_length, self.config.input_dim)
        y_val = np.random.randn(30, self.config.prediction_horizon, self.config.output_dim)
        
        # 训练模型
        history = self.model.train(X_train, y_train, X_val, y_val)
        
        assert self.model.is_trained
        assert history is not None
        assert len(history.history) > 0
    
    def test_model_prediction(self):
        """测试模型预测"""
        # 先构建模型
        self.model.build_model()
        
        # 生成测试数据
        X_test = np.random.randn(5, self.config.sequence_length, self.config.input_dim)
        
        # 进行预测
        predictions = self.model.predict(X_test)
        
        assert predictions is not None
        assert predictions.shape == (5, self.config.prediction_horizon, self.config.output_dim)
    
    def test_model_save_load(self, tmp_path):
        """测试模型保存和加载"""
        # 构建并训练模型
        self.model.build_model()
        
        # 生成少量训练数据
        X_train = np.random.randn(50, self.config.sequence_length, self.config.input_dim)
        y_train = np.random.randn(50, self.config.prediction_horizon, self.config.output_dim)
        
        self.model.train(X_train, y_train)
        
        # 保存模型
        model_path = tmp_path / "test_model.h5"
        self.model.save_model(str(model_path))
        
        assert model_path.exists()
        
        # 创建新模型实例并加载
        new_model = LSTMTrajectoryModel(self.config)
        new_model.load_model(str(model_path))
        
        assert new_model.is_trained
        assert new_model.model is not None
        
        # 测试预测一致性
        X_test = np.random.randn(1, self.config.sequence_length, self.config.input_dim)
        pred1 = self.model.predict(X_test)
        pred2 = new_model.predict(X_test)
        
        np.testing.assert_array_almost_equal(pred1, pred2, decimal=5)
    
    def test_model_evaluation(self):
        """测试模型评估"""
        # 构建并训练模型
        self.model.build_model()
        
        X_train = np.random.randn(50, self.config.sequence_length, self.config.input_dim)
        y_train = np.random.randn(50, self.config.prediction_horizon, self.config.output_dim)
        
        self.model.train(X_train, y_train)
        
        # 评估模型
        X_test = np.random.randn(20, self.config.sequence_length, self.config.input_dim)
        y_test = np.random.randn(20, self.config.prediction_horizon, self.config.output_dim)
        
        metrics = self.model.evaluate(X_test, y_test)
        
        assert isinstance(metrics, dict)
        assert 'loss' in metrics
        assert 'mae' in metrics
    
    def test_get_model_summary(self):
        """测试获取模型摘要"""
        # 未构建模型时
        summary = self.model.get_model_summary()
        assert summary == "模型未构建"
        
        # 构建模型后
        self.model.build_model()
        summary = self.model.get_model_summary()
        assert len(summary) > 0
        assert "LSTM_Trajectory_Model" in summary


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
