"""
API演示脚本
演示如何使用REST API进行航迹预测
"""

import requests
import json
import time
from datetime import datetime, timedelta
import threading

# 设置日志
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class APIClient:
    """API客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def check_health(self) -> bool:
        """检查API服务健康状态"""
        try:
            response = self.session.get(f"{self.base_url}/health")
            if response.status_code == 200:
                data = response.json()
                logger.info(f"API服务状态: {data['status']}")
                return True
            else:
                logger.error(f"健康检查失败: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"连接API服务失败: {e}")
            return False
    
    def send_radar_signal(self, signal_data: dict) -> bool:
        """发送雷达信号数据"""
        try:
            response = self.session.post(
                f"{self.base_url}/radar/signal",
                json=signal_data
            )
            
            if response.status_code == 200:
                return True
            else:
                logger.error(f"发送信号失败: {response.status_code}, {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"发送雷达信号失败: {e}")
            return False
    
    def get_target_prediction(self, target_id: str) -> dict:
        """获取目标预测结果"""
        try:
            response = self.session.get(f"{self.base_url}/prediction/{target_id}")
            
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 404:
                logger.warning(f"目标 {target_id} 未找到或数据不足")
                return {}
            else:
                logger.error(f"获取预测失败: {response.status_code}")
                return {}
                
        except Exception as e:
            logger.error(f"获取预测结果失败: {e}")
            return {}
    
    def get_all_predictions(self) -> dict:
        """获取所有目标的预测结果"""
        try:
            response = self.session.get(f"{self.base_url}/predictions")
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"获取所有预测失败: {response.status_code}")
                return {}
                
        except Exception as e:
            logger.error(f"获取所有预测结果失败: {e}")
            return {}
    
    def get_targets_status(self) -> dict:
        """获取目标状态信息"""
        try:
            response = self.session.get(f"{self.base_url}/targets/status")
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"获取目标状态失败: {response.status_code}")
                return {}
                
        except Exception as e:
            logger.error(f"获取目标状态失败: {e}")
            return {}


def generate_sample_signals(target_id: str, num_signals: int = 25) -> List[dict]:
    """生成示例雷达信号"""
    signals = []
    base_time = datetime.now()
    
    # 初始状态
    x, y, z = 1000, 2000, 10000
    vx, vy, vz = 150, 50, 0
    
    for i in range(num_signals):
        # 更新位置
        x += vx * 1.0 + (i * 10)  # 添加一些变化
        y += vy * 1.0 + (i * 5)
        z += vz * 1.0
        
        # 添加噪声
        noise_x = (i % 3 - 1) * 20
        noise_y = (i % 5 - 2) * 15
        noise_z = (i % 7 - 3) * 10
        
        signal = {
            "timestamp": (base_time + timedelta(seconds=i)).isoformat(),
            "station_id": "radar_001",
            "target_id": target_id,
            "toa": 0.1 + i * 0.01,
            "pa": 3.14159/4 + i * 0.02,
            "position": [x + noise_x, y + noise_y, z + noise_z],
            "velocity": [vx, vy, vz],
            "heading": 3.14159/6,
            "elevation": 3.14159/12,
            "pitch": 0.1,
            "roll": 0.05,
            "snr": 20.0,
            "confidence": 0.9
        }
        signals.append(signal)
    
    return signals


def simulate_real_time_data(client: APIClient, target_id: str, duration: int = 30):
    """模拟实时数据发送"""
    logger.info(f"开始为目标 {target_id} 模拟实时数据...")
    
    for i in range(duration):
        # 生成单个信号
        signals = generate_sample_signals(target_id, 1)
        signal = signals[0]
        
        # 更新时间戳为当前时间
        signal["timestamp"] = datetime.now().isoformat()
        
        # 发送信号
        success = client.send_radar_signal(signal)
        if success:
            logger.debug(f"发送信号成功: {target_id}, 第{i+1}个")
        
        time.sleep(1)  # 每秒发送一次
    
    logger.info(f"目标 {target_id} 数据模拟完成")


def main():
    """主演示函数"""
    logger.info("开始API演示...")
    
    # 创建API客户端
    client = APIClient()
    
    # 检查API服务状态
    logger.info("检查API服务状态...")
    if not client.check_health():
        logger.error("API服务不可用，请先启动API服务")
        logger.info("启动命令: python src/api/rest_api.py")
        return
    
    # 发送一批历史数据
    logger.info("发送历史雷达数据...")
    target_ids = ["target_001", "target_002"]
    
    for target_id in target_ids:
        signals = generate_sample_signals(target_id, 25)
        
        for signal in signals:
            success = client.send_radar_signal(signal)
            if not success:
                logger.error(f"发送信号失败: {target_id}")
                break
        
        logger.info(f"目标 {target_id} 历史数据发送完成")
    
    # 等待一段时间让预测引擎处理数据
    logger.info("等待预测引擎处理数据...")
    time.sleep(5)
    
    # 获取预测结果
    logger.info("获取预测结果...")
    for target_id in target_ids:
        prediction = client.get_target_prediction(target_id)
        
        if prediction:
            logger.info(f"=== 目标 {target_id} 预测结果 ===")
            logger.info(f"数据点数: {prediction['data_points']}")
            logger.info(f"当前位置: {prediction['last_position']}")
            logger.info(f"当前速度: {prediction['last_velocity']}")
            
            if prediction.get('tactical_prediction'):
                tactical = prediction['tactical_prediction']
                logger.info(f"战术模式: {tactical['predicted_mode']} (置信度: {tactical['confidence']:.3f})")
            
            if prediction.get('trajectory_prediction'):
                traj = prediction['trajectory_prediction']
                logger.info(f"预测轨迹: {len(traj)}个时间步")
                logger.info(f"预测终点: {traj[-1]}")
            
            logger.info("")
    
    # 获取所有目标状态
    logger.info("获取所有目标状态...")
    status = client.get_targets_status()
    if status:
        logger.info(f"总目标数: {status['total_targets']}")
        for target_id, info in status['targets'].items():
            logger.info(f"{target_id}: {info['data_points']}个数据点, "
                       f"预测状态: {'有' if info['has_prediction'] else '无'}")
    
    logger.info("API演示完成！")


if __name__ == "__main__":
    main()
